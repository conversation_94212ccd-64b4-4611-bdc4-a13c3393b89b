@extends('layouts/layoutMaster')

@section('title', 'Chỉnh Sửa Lo<PERSON> T<PERSON>ản')

@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/select2/select2.scss',
  'resources/assets/vendor/libs/@form-validation/form-validation.scss',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss'
])
@endsection

@section('vendor-script')
@vite([
  'resources/assets/vendor/libs/select2/select2.js',
  'resources/assets/vendor/libs/@form-validation/popular.js',
  'resources/assets/vendor/libs/@form-validation/bootstrap5.js',
  'resources/assets/vendor/libs/@form-validation/auto-focus.js',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js'
])
@endsection

@section('page-script')
@vite([
  'resources/js/asset-type-form.js'
])
@endsection

@section('content')
<div class="row">
  <div class="col-12">
    <div class="card mb-6">
      <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">Chỉnh Sửa Loại Tài Sản: {{ $assetType->name }}</h5>
        <div class="d-flex gap-2">
          <a href="{{ route('admin.asset-types.show', $assetType) }}" class="btn btn-outline-info">
            <i class="ri-eye-line me-1"></i>Xem
          </a>
          <a href="{{ route('admin.asset-types.index') }}" class="btn btn-outline-secondary">
            <i class="ri-arrow-left-line me-1"></i>Quay lại
          </a>
        </div>
      </div>
      <div class="card-body">
        <form action="{{ route('admin.asset-types.update', $assetType) }}" method="POST" enctype="multipart/form-data" id="assetTypeForm" novalidate>
          @csrf
          @method('PUT')
          @include('admin.asset-types.partials.form')
        </form>
      </div>
    </div>
  </div>
</div>

@if($errors->any())
<script>
document.addEventListener('DOMContentLoaded', function() {
  let errorMessages = '';
  @foreach($errors->all() as $error)
    errorMessages += '{{ $error }}\n';
  @endforeach
  
  Swal.fire({
    title: 'Lỗi xác thực!',
    text: errorMessages,
    icon: 'error',
    customClass: {
      confirmButton: 'btn btn-danger'
    }
  });
});
</script>
@endif
@endsection
