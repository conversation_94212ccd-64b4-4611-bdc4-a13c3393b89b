@extends('layouts/layoutMaster')

@section('title', 'Quản L<PERSON>')

@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-buttons-bs5/buttons.bootstrap5.scss',
  'resources/assets/vendor/libs/select2/select2.scss',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss',
  'resources/css/asset-types.css'
])
@endsection

@section('vendor-script')
@vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js',
  'resources/assets/vendor/libs/select2/select2.js',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js'
])
@endsection

@section('page-script')
@vite([
  'resources/js/datatables-config.js',
  'resources/js/asset-type-management.js'
])
@endsection

@section('content')
<!-- Statistics Cards -->
<div class="row g-6 mb-6">
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div class="me-1">
            <p class="text-heading mb-1">Tổng Loại Tài Sản</p>
            <div class="d-flex align-items-center">
              <h4 class="mb-1 me-2">{{ $totalCount }}</h4>
            </div>
            <small class="mb-0">Tất cả loại tài sản</small>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-primary rounded-3">
              <div class="ri-file-list-line ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div class="me-1">
            <p class="text-heading mb-1">Đang Hoạt Động</p>
            <div class="d-flex align-items-center">
              <h4 class="mb-1 me-2">{{ $activeCount }}</h4>
            </div>
            <small class="mb-0">Loại tài sản đang sử dụng</small>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-success rounded-3">
              <div class="ri-check-line ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div class="me-1">
            <p class="text-heading mb-1">Không Hoạt Động</p>
            <div class="d-flex align-items-center">
              <h4 class="mb-1 me-2">{{ $inactiveCount }}</h4>
            </div>
            <small class="mb-0">Loại tài sản tạm dừng</small>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-secondary rounded-3">
              <div class="ri-close-line ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div class="me-1">
            <p class="text-heading mb-1">Mới Nhất</p>
            <div class="d-flex align-items-center">
              <h4 class="mb-1 me-2">{{ \App\Models\AssetType::whereDate('created_at', today())->count() }}</h4>
            </div>
            <small class="mb-0">Tạo hôm nay</small>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-info rounded-3">
              <div class="ri-add-line ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Filters -->
<div class="card mb-6">
  <div class="card-body">
    <div class="row g-3">
      <div class="col-md-3">
        <div class="form-floating form-floating-outline">
          <select id="StatusFilter" class="form-select">
            <option value="">Tất cả trạng thái</option>
            <option value="active">Đang hoạt động</option>
            <option value="inactive">Không hoạt động</option>
          </select>
          <label for="StatusFilter">Trạng thái</label>
        </div>
      </div>
      <div class="col-md-3">
        <button type="button" class="btn btn-outline-secondary" id="clearFilters">
          <i class="ri-refresh-line me-1"></i>Xóa bộ lọc
        </button>
      </div>
    </div>
  </div>
</div>

<!-- DataTable -->
<div class="card">
  <div class="card-header d-flex justify-content-between align-items-center">
    <h5 class="card-title mb-0">Danh Sách Loại Tài Sản</h5>
    <a href="{{ route('admin.asset-types.create') }}" class="btn btn-primary">
      <i class="ri-add-line me-1"></i>Thêm Mới
    </a>
  </div>
  <div class="card-datatable table-responsive">
    <table class="datatables-asset-types table table-bordered">
      <thead>
        <tr>
          <th></th>
          <th>ID</th>
          <th>Tên</th>
          <th>Mô tả</th>
          <th>Icon</th>
          <th>Trạng thái</th>
          <th>Thứ tự</th>
          <th>Ngày tạo</th>
          <th>Thao tác</th>
        </tr>
      </thead>
    </table>
  </div>
</div>

@if(session('success'))
<script>
document.addEventListener('DOMContentLoaded', function() {
  Swal.fire({
    title: 'Thành công!',
    text: '{{ session('success') }}',
    icon: 'success',
    customClass: {
      confirmButton: 'btn btn-primary'
    }
  });
});
</script>
@endif

@if(session('error'))
<script>
document.addEventListener('DOMContentLoaded', function() {
  Swal.fire({
    title: 'Lỗi!',
    text: '{{ session('error') }}',
    icon: 'error',
    customClass: {
      confirmButton: 'btn btn-danger'
    }
  });
});
</script>
@endif
@endsection
