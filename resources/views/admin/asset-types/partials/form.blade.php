{{-- resources/views/admin/asset-types/partials/form.blade.php --}}
<div class="row">
  <div class="col-md-8">
    <div class="card mb-6">
      <div class="card-header">
        <h5 class="card-title mb-0">Thông Tin Cơ Bản</h5>
      </div>
      <div class="card-body">
        <div class="row g-3">
          <div class="col-md-12">
            <div class="form-floating form-floating-outline">
              <input type="text"
                     class="form-control @error('name') is-invalid @enderror"
                     id="name"
                     name="name"
                     value="{{ old('name', $assetType->name ?? '') }}"
                     placeholder="Nhập tên loại tài sản">
              <label for="name">Tên Loại Tài Sản *</label>
              @error('name')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
          </div>

          <div class="col-md-12">
            <div class="form-floating form-floating-outline">
              <textarea class="form-control @error('description') is-invalid @enderror"
                        id="description"
                        name="description"
                        rows="4"
                        placeholder="Nhập mô tả loại tài sản">{{ old('description', $assetType->description ?? '') }}</textarea>
              <label for="description">Mô Tả</label>
              @error('description')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
          </div>

          <div class="col-md-12">
            <div class="form-floating form-floating-outline">
              <input type="text"
                     class="form-control @error('icon') is-invalid @enderror"
                     id="icon"
                     name="icon"
                     value="{{ old('icon', $assetType->icon ?? '') }}"
                     placeholder="VD: ri-file-line hoặc đường dẫn file">
              <label for="icon">Icon</label>
              <div class="form-text">
                Nhập class RemixIcon (VD: ri-file-line) hoặc đường dẫn đến file icon
              </div>
              @error('icon')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
          </div>

          <div class="col-md-12">
            <label for="image" class="form-label">Hình Ảnh Đại Diện</label>
            <input type="file"
                   class="form-control @error('image') is-invalid @enderror"
                   id="image"
                   name="image"
                   accept="image/*">
            <div class="form-text">
              Chấp nhận: JPEG, PNG, JPG, GIF, SVG. Kích thước tối đa: 2MB
            </div>
            @error('image')
              <div class="invalid-feedback">{{ $message }}</div>
            @enderror

            @if(isset($assetType) && $assetType->image_url)
              <div class="mt-3">
                <p class="mb-2"><strong>Hình ảnh hiện tại:</strong></p>
                <img src="{{ $assetType->image_url }}"
                     alt="{{ $assetType->name }}"
                     class="img-thumbnail"
                     style="max-width: 150px; max-height: 150px;">
                <div class="form-check mt-2">
                  <input class="form-check-input" type="checkbox" id="remove_image" name="remove_image" value="1">
                  <label class="form-check-label" for="remove_image">
                    Xóa hình ảnh hiện tại
                  </label>
                </div>
              </div>
            @endif
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="col-md-4">
    <div class="card mb-6">
      <div class="card-header">
        <h5 class="card-title mb-0">Cài Đặt</h5>
      </div>
      <div class="card-body">
        <div class="row g-3">
          <div class="col-12">
            <div class="form-floating form-floating-outline">
              <select class="form-select @error('status') is-invalid @enderror"
                      id="status"
                      name="status">
                <option value="active" {{ old('status', $assetType->status ?? 'active') === 'active' ? 'selected' : '' }}>
                  Đang hoạt động
                </option>
                <option value="inactive" {{ old('status', $assetType->status ?? '') === 'inactive' ? 'selected' : '' }}>
                  Không hoạt động
                </option>
              </select>
              <label for="status">Trạng Thái *</label>
              @error('status')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
          </div>

          <div class="col-12">
            <div class="form-floating form-floating-outline">
              <input type="number"
                     class="form-control @error('sort_order') is-invalid @enderror"
                     id="sort_order"
                     name="sort_order"
                     value="{{ old('sort_order', $assetType->sort_order ?? '') }}"
                     min="0"
                     max="999999"
                     step="1"
                     placeholder="Để trống để tự động tạo">
              <label for="sort_order">Thứ Tự Sắp Xếp</label>
              <div class="form-text">
                Số càng nhỏ sẽ hiển thị trước
              </div>
              @error('sort_order')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Preview Card -->
    <div class="card mb-6" id="previewCard" style="display: none;">
      <div class="card-header">
        <h6 class="card-title mb-0">Xem Trước</h6>
      </div>
      <div class="card-body text-center">
        <div id="iconPreview" class="mb-2"></div>
        <div id="namePreview" class="fw-medium"></div>
        <div id="statusPreview" class="mt-2"></div>
      </div>
    </div>

    <div class="card">
      <div class="card-body">
        <div class="d-grid gap-2">
          <button type="submit" class="btn btn-primary">
            <i class="ri-save-line me-1"></i>
            {{ isset($assetType) ? 'Cập Nhật' : 'Tạo Mới' }}
          </button>
          <a href="{{ route('admin.asset-types.index') }}" class="btn btn-outline-secondary">
            <i class="ri-arrow-left-line me-1"></i>Quay Lại
          </a>
          @if(isset($assetType))
            <hr class="my-2">
            <a href="{{ route('admin.asset-types.show', $assetType) }}" class="btn btn-outline-info">
              <i class="ri-eye-line me-1"></i>Xem Chi Tiết
            </a>
          @endif
        </div>
      </div>
    </div>
  </div>
</div>
