/**
 * Asset Type Form JavaScript
 */

'use strict';

$(function () {
  // Form validation
  const form = document.getElementById('assetTypeForm');

  if (form) {
    const fv = FormValidation.formValidation(form, {
      fields: {
        name: {
          validators: {
            notEmpty: {
              message: 'Tên loại tài sản là bắt buộc'
            },
            stringLength: {
              min: 2,
              max: 255,
              message: 'Tên loại tài sản phải từ 2 đến 255 ký tự'
            },
            regexp: {
              regexp: /^[a-zA-ZÀ-ỹ0-9\s\-_\.]+$/,
              message: 'Tên loại tài sản chỉ được chứa chữ cái, số, dấu cách, gạch ngang và gạch dưới'
            }
          }
        },
        description: {
          validators: {
            stringLength: {
              max: 1000,
              message: 'Mô tả không được vượt quá 1000 ký tự'
            }
          }
        },
        icon: {
          validators: {
            stringLength: {
              max: 255,
              message: 'Icon không được vượt quá 255 ký tự'
            },
            callback: {
              message: 'Icon phải là class RemixIcon (ri-*) hoặc đường dẫn file hợp lệ',
              callback: function(input) {
                const value = input.value;
                if (!value) return true; // Optional field

                // Check if it's a RemixIcon class
                if (value.startsWith('ri-')) {
                  return /^ri-[a-z0-9\-]+$/.test(value);
                }

                // Check if it's a valid file path
                return /^[a-zA-Z0-9\/_\-\.]+$/.test(value);
              }
            }
          }
        },
        image: {
          validators: {
            file: {
              extension: 'jpeg,jpg,png,gif,svg',
              type: 'image/jpeg,image/jpg,image/png,image/gif,image/svg+xml',
              maxSize: 2097152, // 2MB
              message: 'Vui lòng chọn file hình ảnh hợp lệ (JPEG, PNG, GIF, SVG) và không quá 2MB'
            }
          }
        },
        status: {
          validators: {
            notEmpty: {
              message: 'Trạng thái là bắt buộc'
            },
            choice: {
              min: 1,
              message: 'Vui lòng chọn trạng thái'
            }
          }
        },
        sort_order: {
          validators: {
            callback: {
              message: 'Thứ tự sắp xếp phải là số nguyên từ 0 đến 999999',
              callback: function(input) {
                const value = input.value;

                // Allow empty value (nullable field)
                if (!value || value === '') {
                  return true;
                }

                // Check if it's a valid integer
                const num = parseInt(value, 10);
                if (isNaN(num) || num.toString() !== value.toString()) {
                  return false;
                }

                // Check range
                return num >= 0 && num <= 999999;
              }
            }
          }
        }
      },
      plugins: {
        trigger: new FormValidation.plugins.Trigger(),
        bootstrap5: new FormValidation.plugins.Bootstrap5({
          eleValidClass: '',
          rowSelector: '.form-floating, .input-group, .col-md-12, .col-12'
        }),
        submitButton: new FormValidation.plugins.SubmitButton(),
        autoFocus: new FormValidation.plugins.AutoFocus()
      }
    });

    // Real-time preview functionality
    function updatePreview() {
      const name = $('#name').val();
      const icon = $('#icon').val();
      const status = $('#status').val();

      if (name || icon || status) {
        $('#previewCard').show();

        // Update name
        $('#namePreview').text(name || 'Tên loại tài sản');

        // Update icon
        let iconHtml = '<i class="ri-file-line ri-22px text-muted"></i>';
        if (icon) {
          if (icon.startsWith('ri-')) {
            iconHtml = '<i class="' + icon + ' ri-22px text-primary"></i>';
          } else {
            iconHtml = '<i class="ri-image-line ri-22px text-info"></i>';
          }
        }
        $('#iconPreview').html(iconHtml);

        // Update status
        const statusClass = status === 'active' ? 'success' : 'secondary';
        const statusText = status === 'active' ? 'Đang hoạt động' : 'Không hoạt động';
        $('#statusPreview').html('<span class="badge bg-label-' + statusClass + '">' + statusText + '</span>');
      } else {
        $('#previewCard').hide();
      }
    }

    // Bind preview update events
    $('#name, #icon, #status').on('input change', updatePreview);

    // Initial preview update
    updatePreview();

    // Icon input helper
    $('#icon').on('input', function() {
      const value = $(this).val();
      const helpText = $(this).siblings('.form-text');

      if (value.startsWith('ri-')) {
        helpText.html('RemixIcon class detected. <a href="https://remixicon.com/" target="_blank">Browse icons</a>');
      } else if (value) {
        helpText.html('File path detected. Make sure the file exists in storage.');
      } else {
        helpText.html('Nhập class RemixIcon (VD: ri-file-line) hoặc đường dẫn đến file icon');
      }
    });

    // Image preview functionality
    $('#image').on('change', function() {
      const file = this.files[0];
      const preview = $('#imagePreview');

      if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
          if (preview.length === 0) {
            $(this).after('<div id="imagePreview" class="mt-2"><img src="' + e.target.result + '" class="img-thumbnail" style="max-width: 150px; max-height: 150px;"></div>');
          } else {
            preview.html('<img src="' + e.target.result + '" class="img-thumbnail" style="max-width: 150px; max-height: 150px;">');
          }
        };
        reader.readAsDataURL(file);
      } else {
        preview.remove();
      }
    });

    // Remove image checkbox handling
    $('#remove_image').on('change', function() {
      if ($(this).is(':checked')) {
        $('#image').val('').trigger('change');
        $(this).closest('.mt-3').find('img').css('opacity', '0.5');
      } else {
        $(this).closest('.mt-3').find('img').css('opacity', '1');
      }
    });

    // Form submission handling
    form.addEventListener('submit', function(e) {
      // Additional client-side validation can be added here

      // Show loading state
      const submitBtn = form.querySelector('button[type="submit"]');
      const originalText = submitBtn.innerHTML;

      submitBtn.disabled = true;
      submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status"></span>Đang xử lý...';

      // Reset button state after a delay (in case of server errors)
      setTimeout(() => {
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
      }, 10000);
    });
  }

  // Character counter for description
  $('#description').on('input', function() {
    const maxLength = 1000;
    const currentLength = $(this).val().length;
    const remaining = maxLength - currentLength;

    let counterHtml = '<small class="text-muted">' + currentLength + '/' + maxLength + ' ký tự</small>';
    if (remaining < 100) {
      counterHtml = '<small class="text-warning">' + currentLength + '/' + maxLength + ' ký tự</small>';
    }
    if (remaining < 0) {
      counterHtml = '<small class="text-danger">' + currentLength + '/' + maxLength + ' ký tự</small>';
    }

    $(this).siblings('.form-text').html(counterHtml);
  });

  // Auto-generate sort order
  if ($('#sort_order').val() === '' || $('#sort_order').val() === '0') {
    // This could be enhanced to fetch the next available sort order via AJAX
    $('#sort_order').attr('placeholder', 'Sẽ tự động tạo nếu để trống');
  }

  // Sort order input validation helper
  $('#sort_order').on('input', function() {
    const value = $(this).val();
    const helpText = $(this).siblings('.form-text');

    if (value === '' || value === '0') {
      helpText.html('Để trống hoặc 0 để tự động tạo thứ tự');
    } else if (parseInt(value) < 0) {
      helpText.html('<span class="text-danger">Thứ tự phải lớn hơn hoặc bằng 0</span>');
    } else if (parseInt(value) > 999999) {
      helpText.html('<span class="text-danger">Thứ tự không được vượt quá 999999</span>');
    } else {
      helpText.html('Số càng nhỏ sẽ hiển thị trước');
    }
  });

  // Prevent negative values in sort_order input
  $('#sort_order').on('keydown', function(e) {
    // Allow: backspace, delete, tab, escape, enter
    if ($.inArray(e.keyCode, [46, 8, 9, 27, 13]) !== -1 ||
        // Allow: Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
        (e.keyCode === 65 && e.ctrlKey === true) ||
        (e.keyCode === 67 && e.ctrlKey === true) ||
        (e.keyCode === 86 && e.ctrlKey === true) ||
        (e.keyCode === 88 && e.ctrlKey === true) ||
        // Allow: home, end, left, right
        (e.keyCode >= 35 && e.keyCode <= 39)) {
      return;
    }
    // Ensure that it is a number and stop the keypress
    if ((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57)) && (e.keyCode < 96 || e.keyCode > 105)) {
      e.preventDefault();
    }
  });
});
