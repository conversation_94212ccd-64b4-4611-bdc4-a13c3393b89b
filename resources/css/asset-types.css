/**
 * Asset Types Custom Styles
 */

/* Preview Card Styles */
#previewCard {
  transition: all 0.3s ease;
}

#previewCard .card-body {
  padding: 1.5rem;
}

#iconPreview {
  font-size: 2rem;
  line-height: 1;
}

#namePreview {
  font-size: 1.1rem;
  color: var(--bs-heading-color);
}

/* Form Enhancements */
.form-floating-outline .form-control:focus {
  border-color: var(--bs-primary);
  box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
}

/* Image Preview Styles */
.img-thumbnail {
  border: 2px solid var(--bs-border-color);
  transition: all 0.3s ease;
}

.img-thumbnail:hover {
  border-color: var(--bs-primary);
  transform: scale(1.05);
}

/* Status Badge Enhancements */
.badge.bg-label-success {
  background-color: rgba(var(--bs-success-rgb), 0.1) !important;
  color: var(--bs-success) !important;
}

.badge.bg-label-secondary {
  background-color: rgba(var(--bs-secondary-rgb), 0.1) !important;
  color: var(--bs-secondary) !important;
}

/* DataTable Custom Styles */
.datatables-asset-types .btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

.datatables-asset-types .btn-sm i {
  font-size: 0.875rem;
}

/* Action Buttons */
.btn-outline-info:hover {
  color: #fff;
  background-color: var(--bs-info);
  border-color: var(--bs-info);
}

.btn-outline-primary:hover {
  color: #fff;
  background-color: var(--bs-primary);
  border-color: var(--bs-primary);
}

.btn-outline-warning:hover {
  color: #fff;
  background-color: var(--bs-warning);
  border-color: var(--bs-warning);
}

.btn-outline-success:hover {
  color: #fff;
  background-color: var(--bs-success);
  border-color: var(--bs-success);
}

.btn-outline-danger:hover {
  color: #fff;
  background-color: var(--bs-danger);
  border-color: var(--bs-danger);
}

/* Statistics Cards */
.card .avatar-initial {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Responsive Enhancements */
@media (max-width: 768px) {
  .card-header .d-flex {
    flex-direction: column;
    gap: 1rem;
  }
  
  .card-header .btn {
    width: 100%;
  }
  
  .datatables-asset-types .btn-sm {
    padding: 0.125rem 0.25rem;
    font-size: 0.7rem;
  }
  
  #previewCard {
    margin-top: 1rem;
  }
}

/* Dark Mode Adjustments */
[data-bs-theme="dark"] .img-thumbnail {
  border-color: var(--bs-border-color-translucent);
}

[data-bs-theme="dark"] .form-floating-outline .form-control {
  background-color: var(--bs-body-bg);
  border-color: var(--bs-border-color);
}

[data-bs-theme="dark"] .card {
  background-color: var(--bs-card-bg);
  border-color: var(--bs-border-color);
}

/* Loading States */
.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.spinner-border-sm {
  width: 0.875rem;
  height: 0.875rem;
}

/* Form Validation Enhancements */
.is-invalid {
  border-color: var(--bs-danger) !important;
}

.invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: var(--bs-danger);
}

/* Icon Preview in Form */
.icon-preview-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
  padding: 0.5rem;
  background-color: var(--bs-light);
  border-radius: 0.375rem;
  border: 1px solid var(--bs-border-color);
}

[data-bs-theme="dark"] .icon-preview-container {
  background-color: var(--bs-dark);
}
