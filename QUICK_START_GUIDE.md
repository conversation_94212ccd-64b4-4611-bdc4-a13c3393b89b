# VPCC Laravel Quick Start Guide - Hướng Dẫn Bắt Đầu <PERSON>

## 🚀 Tổng Quan

Đây là hướng dẫn nhanh để bắt đầu phát triển với dự án VPCC Laravel. Tài liệu này tóm tắt các bước cần thiết để tạo một module CRUD hoàn chỉnh theo standards của dự án.

---

## 📋 Checklist Tạo Module Mới

### ✅ Bước 1: Chuẩn Bị
- [ ] Xác định tên module (VD: `ProductCategory`)
- [ ] Thiết kế database schema
- [ ] Xác định relationships với models khác

### ✅ Bước 2: Database
```bash
# Tạo migration
php artisan make:migration create_your_models_table

# Tạo model với factory và seeder
php artisan make:model YourModel -fs
```

### ✅ Bước 3: Backend
```bash
# Tạo controller
php artisan make:controller Admin/YourModelController --resource

# Tạo form requests
php artisan make:request StoreYourModelRequest
php artisan make:request UpdateYourModelRequest
```

### ✅ Bước 4: Frontend
- [ ] Tạo views trong `resources/views/admin/your-models/`
- [ ] Tạo JavaScript file trong `resources/js/`
- [ ] Tạo CSS file trong `resources/css/` (nếu cần)
- [ ] Cập nhật `vite.config.js`

### ✅ Bước 5: Routes & Menu
- [ ] Thêm routes vào `routes/web.php`
- [ ] Cập nhật `resources/menu/verticalMenu.json`

---

## 🎯 Template Files Cần Tạo

### 1. Migration Template
```php
Schema::create('your_models', function (Blueprint $table) {
    $table->id();
    $table->string('name');
    $table->text('description')->nullable();
    $table->enum('status', ['active', 'inactive'])->default('active');
    $table->integer('sort_order')->default(0);
    $table->timestamps();
    
    $table->index(['status', 'sort_order']);
});
```

### 2. Model Template
```php
class YourModel extends Model
{
    protected $fillable = ['name', 'description', 'status', 'sort_order'];
    
    public function scopeActive($query) {
        return $query->where('status', 'active');
    }
    
    public function getStatusBadgeAttribute() {
        $class = $this->status === 'active' ? 'success' : 'secondary';
        return '<span class="badge bg-label-' . $class . '">' . ucfirst($this->status) . '</span>';
    }
}
```

### 3. Controller Template
```php
class YourModelController extends Controller
{
    public function index() {
        return view('admin.your-models.index');
    }
    
    public function getData(Request $request) {
        // DataTables endpoint - see full template
    }
    
    public function store(StoreYourModelRequest $request) {
        YourModel::create($request->validated());
        return redirect()->route('admin.your-models.index')
            ->with('success', 'Item created successfully.');
    }
    
    // ... other CRUD methods
}
```

### 4. View Template (Index)
```php
@extends('layouts/layoutMaster')

@section('title', 'Your Model Management')

@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss',
  'resources/assets/vendor/libs/select2/select2.scss',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss'
])
@endsection

@section('vendor-script')
@vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js',
  'resources/assets/vendor/libs/select2/select2.js',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js'
])
@endsection

@section('page-script')
@vite([
  'resources/js/datatables-config.js',
  'resources/js/your-model-management.js'
])
@endsection

@section('content')
<!-- DataTable -->
<div class="card">
  <div class="card-header d-flex justify-content-between align-items-center">
    <h5 class="card-title mb-0">Your Models List</h5>
    <a href="{{ route('admin.your-models.create') }}" class="btn btn-primary">
      <i class="ri-add-line me-1"></i>Add New
    </a>
  </div>
  <div class="card-datatable table-responsive">
    <table class="datatables-your-models table table-bordered">
      <thead>
        <tr>
          <th></th><th>ID</th><th>Name</th><th>Status</th><th>Created</th><th>Actions</th>
        </tr>
      </thead>
    </table>
  </div>
</div>
@endsection
```

### 5. JavaScript Template
```javascript
'use strict';

$(function () {
  var dt_table = $('.datatables-your-models');

  if (dt_table.length) {
    var defaultConfig = DataTablesConfig.getDefaultConfig(isDarkStyle, config);

    var dt = dt_table.DataTable($.extend(true, {}, defaultConfig, {
      ajax: {
        url: baseUrl + 'admin/your-models-data',
        type: 'GET'
      },
      columns: [
        { data: '' }, { data: 'id' }, { data: 'name' }, 
        { data: 'status_badge' }, { data: 'created_at' }, { data: 'actions' }
      ],
      columnDefs: DataTablesConfig.getCommonColumnDefs().concat([
        {
          targets: 2,
          render: function (data, type, full, meta) {
            return '<span class="fw-medium">' + data + '</span>';
          }
        }
      ])
    }));

    DataTablesConfig.initializeFormControls();
  }

  // Delete functionality
  $(document).on('click', '.delete-record', function () {
    var id = $(this).data('id');
    var name = $(this).data('name');

    Swal.fire({
      title: 'Are you sure?',
      text: 'You want to delete "' + name + '"?',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, delete it!',
      cancelButtonText: 'Cancel',
      customClass: {
        confirmButton: 'btn btn-danger me-2',
        cancelButton: 'btn btn-outline-secondary'
      }
    }).then((result) => {
      if (result.isConfirmed) {
        $.ajax({
          url: baseUrl + 'admin/your-models/' + id,
          type: 'DELETE',
          headers: { 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') },
          success: function(response) {
            dt.ajax.reload();
            Swal.fire({
              title: 'Deleted!',
              text: 'Record has been deleted.',
              icon: 'success',
              customClass: { confirmButton: 'btn btn-success' }
            });
          }
        });
      }
    });
  });
});
```

### 6. Routes Template
```php
Route::prefix('admin')->name('admin.')->middleware(['auth'])->group(function () {
    Route::resource('your-models', YourModelController::class);
    Route::get('your-models-data', [YourModelController::class, 'getData'])->name('your-models.data');
});
```

---

## ⚡ Vite Configuration

### Thêm Assets vào vite.config.js
```javascript
// Trong input array, thêm:
'resources/css/your-model.css',    // Custom CSS
'resources/js/your-model-management.js',  // Custom JS
```

### Build Assets
```bash
# Development
npm run dev

# Production
npm run build
```

---

## 🎨 Form Components Quick Reference

### Select2 Basic
```html
<div class="form-floating form-floating-outline">
  <select class="form-select select2" id="select" name="field">
    <option value="">Choose...</option>
  </select>
  <label for="select">Label</label>
</div>
```

```javascript
$('.select2').select2({
  placeholder: 'Choose option...',
  allowClear: true
});
select2Focus('.select2');
```

### Form Validation
```javascript
const fv = FormValidation.formValidation(form, {
  fields: {
    field_name: {
      validators: {
        notEmpty: { message: 'Field is required' },
        stringLength: { min: 2, max: 50, message: 'Between 2-50 characters' }
      }
    }
  },
  plugins: {
    trigger: new FormValidation.plugins.Trigger(),
    bootstrap5: new FormValidation.plugins.Bootstrap5({
      eleValidClass: '',
      rowSelector: '.form-floating, .input-group'
    }),
    submitButton: new FormValidation.plugins.SubmitButton(),
    autoFocus: new FormValidation.plugins.AutoFocus()
  }
});
```

### Date Picker
```html
<input type="text" class="form-control flatpickr-date" placeholder="Select date">
```

```javascript
$('.flatpickr-date').flatpickr({
  dateFormat: 'd/m/Y'
});
```

---

## 📊 DataTables Quick Setup

### Controller Data Method
```php
public function getData(Request $request)
{
    $query = YourModel::query();

    // Search
    if ($request->has('search') && !empty($request->search['value'])) {
        $searchValue = $request->search['value'];
        $query->where('name', 'like', "%{$searchValue}%");
    }

    // Ordering
    if ($request->has('order')) {
        $orderColumn = $request->columns[$request->order[0]['column']]['data'];
        $orderDirection = $request->order[0]['dir'];
        $query->orderBy($orderColumn, $orderDirection);
    }

    // Pagination
    $recordsTotal = YourModel::count();
    $recordsFiltered = $query->count();
    
    $start = $request->start ?? 0;
    $length = $request->length ?? 10;
    
    $data = $query->skip($start)->take($length)->get()->map(function ($item) {
        return [
            'id' => $item->id,
            'name' => $item->name,
            'status_badge' => $item->status_badge,
            'created_at' => $item->created_at->format('d/m/Y'),
            'actions' => view('admin.your-models.partials.actions', compact('item'))->render()
        ];
    });

    return response()->json([
        'draw' => $request->draw,
        'recordsTotal' => $recordsTotal,
        'recordsFiltered' => $recordsFiltered,
        'data' => $data
    ]);
}
```

---

## 🔧 Common Patterns

### Status Badge Helper
```php
// In Model
public function getStatusBadgeAttribute()
{
    $statusConfig = [
        'active' => ['class' => 'success', 'text' => 'Active'],
        'inactive' => ['class' => 'secondary', 'text' => 'Inactive'],
        'pending' => ['class' => 'warning', 'text' => 'Pending']
    ];
    
    $config = $statusConfig[$this->status] ?? $statusConfig['inactive'];
    return '<span class="badge bg-label-' . $config['class'] . '">' . $config['text'] . '</span>';
}
```

### Actions Partial
```php
{{-- resources/views/admin/your-models/partials/actions.blade.php --}}
<div class="d-flex align-items-center gap-2">
  <a href="{{ route('admin.your-models.edit', $item->id) }}" 
     class="btn btn-sm btn-outline-primary" title="Edit">
    <i class="ri-edit-line"></i>
  </a>
  <button type="button" 
          class="btn btn-sm btn-outline-danger delete-record" 
          data-id="{{ $item->id }}" 
          data-name="{{ $item->name }}" 
          title="Delete">
    <i class="ri-delete-bin-line"></i>
  </button>
</div>
```

### Menu Configuration
```json
// resources/menu/verticalMenu.json
{
  "url": "admin/your-models",
  "name": "Your Models",
  "icon": "menu-icon tf-icons ri-file-list-line",
  "slug": "admin-your-models"
}
```

---

## 🎯 Testing Checklist

### Functionality Tests
- [ ] Create new record
- [ ] Edit existing record
- [ ] Delete record with confirmation
- [ ] Search functionality
- [ ] Sorting by columns
- [ ] Pagination
- [ ] Export functionality
- [ ] Responsive design

### Form Tests
- [ ] Required field validation
- [ ] Format validation (email, phone, etc.)
- [ ] Select2 functionality
- [ ] Date picker functionality
- [ ] File upload (if applicable)
- [ ] AJAX form submission
- [ ] Error handling

### UI/UX Tests
- [ ] Dark/Light theme compatibility
- [ ] Mobile responsiveness
- [ ] Loading states
- [ ] Success/Error notifications
- [ ] Accessibility (keyboard navigation)

---

## 📚 Tài Liệu Tham Khảo

### Nội Bộ
- **VPCC_COMPREHENSIVE_DEVELOPMENT_GUIDE.md** - Hướng dẫn phát triển toàn diện
- **templates/CRUD_MODULE_TEMPLATE.md** - Template chi tiết cho CRUD module
- **templates/DATATABLES_IMPLEMENTATION_GUIDE.md** - Hướng dẫn DataTables
- **templates/FORM_COMPONENTS_GUIDE.md** - Hướng dẫn form components

### Thư Viện
- [Laravel 11 Documentation](https://laravel.com/docs/11.x)
- [Bootstrap 5 Documentation](https://getbootstrap.com/docs/5.3/)
- [DataTables Documentation](https://datatables.net/)
- [Select2 Documentation](https://select2.org/)

---

## 🚨 Lưu Ý Quan Trọng

### ❌ KHÔNG ĐƯỢC
- Nhúng JavaScript/CSS trực tiếp trong Blade templates
- Sử dụng Yajra DataTables package
- Modify vendor library files trực tiếp
- Hardcode URLs trong JavaScript

### ✅ PHẢI LÀM
- Sử dụng DataTablesConfig helper
- Apply Select2 cho tất cả dropdowns
- Sử dụng SweetAlert2 cho notifications
- Follow Bootstrap 5 component structure
- Test trên mobile devices

---

*Hướng dẫn này cung cấp foundation nhanh để bắt đầu phát triển module mới trong dự án VPCC. Tham khảo các tài liệu chi tiết khác để hiểu sâu hơn về từng component.*
