# VPCC Laravel Development Guide - Hướng Dẫn Phát Triển Toàn Diện

## 📋 <PERSON>ụ<PERSON>

1. [Tổng Quan Dự Án](#tổng-quan-dự-án)
2. [<PERSON><PERSON> Tích <PERSON>](#phân-tích-kỹ-thuật)
3. [C<PERSON><PERSON> Trúc <PERSON>](#cấu-trúc-dự-án)
4. [Standards và Conventions](#standards-và-conventions)
5. [Hướng Dẫn HTML Templates](#hướng-dẫn-html-templates)
6. [Quản Lý Assets (JS/CSS)](#quản-lý-assets-jscss)
7. [DataTables Implementation](#datatables-implementation)
8. [Form Components](#form-components)
9. [Vite Integration](#vite-integration)
10. [Templates và Examples](#templates-và-examples)

---

## 🎯 Tổng Quan Dự Án

### Thông Tin Cơ Bản
- **Framework**: Laravel 11.x
- **Frontend**: Bootstrap 5 + Materialize Theme
- **Build Tool**: Vite 5.3.3
- **Database**: SQLite (development)
- **Authentication**: Laravel Jetstream + Fortify
- **Permissions**: Spatie Laravel Permission

### Kiến Trúc Hiện Tại
- **Layout Pattern**: Content Navbar Layout với Vertical Menu
- **Theme System**: Materialize Bootstrap với Dark/Light mode support
- **Asset Management**: Vite-based với vendor libraries organization
- **DataTables**: Client-side processing với Bootstrap 5 styling
- **Form Handling**: Select2 + Form Validation + SweetAlert2

---

## 🔧 Phân Tích Kỹ Thuật

### Laravel Dependencies
```json
{
  "laravel/framework": "^11.0",
  "laravel/jetstream": "^5.3",
  "laravel/sanctum": "^4.0",
  "livewire/livewire": "^3.0",
  "spatie/laravel-permission": "^6.20",
  "phpoffice/phpword": "^1.4"
}
```

### Frontend Dependencies
```json
{
  "bootstrap": "5.3.3",
  "datatables.net-bs5": "1.13.11",
  "select2": "4.0.13",
  "sweetalert2": "11.10.8",
  "@form-validation/bundle": "2.4.0",
  "flatpickr": "4.6.13",
  "jquery": "3.7.1"
}
```

### Build Configuration
- **Vite**: Configured với auto-discovery cho JS/CSS files
- **SCSS Support**: Vendor libraries với Bootstrap 5 theming
- **Asset Optimization**: Automatic bundling và minification
- **Hot Reload**: Development server với live reloading

---

## 📁 Cấu Trúc Dự Án

### Thư Mục Resources
```
resources/
├── assets/                 # Vendor libraries và theme assets
│   ├── css/               # Demo CSS và theme styles
│   ├── js/                # Page-specific JavaScript files
│   └── vendor/            # Third-party libraries
│       ├── libs/          # UI libraries (DataTables, Select2, etc.)
│       ├── scss/          # Theme SCSS files
│       └── js/            # Vendor JavaScript
├── css/                   # Custom CSS files cho modules
├── js/                    # Custom JavaScript files cho modules
├── views/                 # Blade templates
│   ├── template/          # Theme templates và examples
│   ├── admin/             # Admin module views
│   ├── layouts/           # Layout templates
│   └── components/        # Reusable components
└── menu/                  # Menu configuration files
```

### App Structure
```
app/
├── Http/Controllers/      # Controllers theo modules
│   ├── Admin/            # Admin functionality
│   ├── pages/            # Static pages
│   └── language/         # Localization
├── Models/               # Eloquent models
├── Helpers/              # Helper classes
├── Services/             # Business logic services
└── Policies/             # Authorization policies
```

---

## 📐 Standards và Conventions

### Naming Conventions
- **Controllers**: PascalCase với suffix `Controller` (VD: `UserController`)
- **Models**: PascalCase singular (VD: `User`, `Role`)
- **Views**: kebab-case (VD: `user-management.blade.php`)
- **JavaScript**: camelCase cho functions, kebab-case cho files
- **CSS Classes**: Bootstrap 5 conventions + custom prefixes

### File Organization
- **Blade Templates**: Sử dụng `resources/views/template` làm reference
- **JavaScript**: Tất cả custom JS trong `resources/js/`
- **CSS**: Tất cả custom CSS trong `resources/css/`
- **Vendor Assets**: Giữ nguyên trong `resources/assets/vendor/`

### Code Style
- **PHP**: PSR-12 coding standard
- **JavaScript**: ES6+ với strict mode
- **CSS**: BEM methodology cho custom classes
- **Blade**: Indentation 2 spaces, semantic HTML5

---

## 🎨 Hướng Dẫn HTML Templates

### Layout Structure
```php
@extends('layouts/layoutMaster')

@section('title', 'Page Title')

@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss',
  'resources/assets/vendor/libs/select2/select2.scss'
])
@endsection

@section('vendor-script')
@vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js',
  'resources/assets/vendor/libs/select2/select2.js'
])
@endsection

@section('page-script')
@vite([
  'resources/js/custom-module.js'
])
@endsection

@section('content')
<!-- Page content here -->
@endsection
```

### Template Reference
- **Base Layout**: `resources/views/layouts/layoutMaster.blade.php`
- **Content Layout**: `resources/views/layouts/contentNavbarLayout.blade.php`
- **Table Examples**: `resources/views/template/tables/`
- **Form Examples**: `resources/views/template/form-elements/`

### HTML Structure Guidelines
```html
<!-- Card Container -->
<div class="card">
  <div class="card-header d-flex justify-content-between align-items-center">
    <h5 class="card-title mb-0">Title</h5>
    <button class="btn btn-primary">Action</button>
  </div>
  <div class="card-body">
    <!-- Content -->
  </div>
</div>

<!-- DataTable Container -->
<div class="card">
  <div class="card-datatable table-responsive">
    <table class="datatables-basic table table-bordered">
      <!-- Table structure -->
    </table>
  </div>
</div>
```

---

## 🎯 Quản Lý Assets (JS/CSS)

### Quy Tắc Cơ Bản
1. **NGHIÊM CẤM** nhúng JavaScript hoặc CSS trực tiếp trong Blade templates
2. Tất cả custom JavaScript phải được đặt trong `resources/js/`
3. Tất cả custom CSS phải được đặt trong `resources/css/`
4. Sử dụng Vite để build và reference assets

### Vite Configuration Pattern
```javascript
// vite.config.js
export default defineConfig({
  plugins: [
    laravel({
      input: [
        'resources/css/app.css',
        'resources/js/app.js',
        // Custom module files
        'resources/css/module-name.css',
        'resources/js/module-name.js',
        // Vendor files (auto-discovered)
        ...pageJsFiles,
        ...vendorJsFiles,
        ...LibsJsFiles,
        ...CoreScssFiles
      ],
      refresh: true
    })
  ]
});
```

### Asset Loading trong Blade
```php
<!-- CSS Assets -->
@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/library-name/library.scss'
])
@endsection

<!-- JavaScript Assets -->
@section('vendor-script')
@vite([
  'resources/assets/vendor/libs/library-name/library.js'
])
@endsection

@section('page-script')
@vite([
  'resources/js/custom-script.js'
])
@endsection
```

### Custom Module Pattern
```javascript
// resources/js/module-name.js
'use strict';

$(function () {
  // Module initialization
  const ModuleName = {
    init: function() {
      this.bindEvents();
      this.initializeComponents();
    },

    bindEvents: function() {
      // Event handlers
    },

    initializeComponents: function() {
      // Component initialization
    }
  };

  // Initialize module
  ModuleName.init();
});
```

---

## 📊 DataTables Implementation

### Cấu Hình Cơ Bản
Dự án sử dụng **client-side DataTables** với Bootstrap 5 styling. Tham khảo `resources/js/datatables-config.js` cho configuration helper.

### Standard Implementation
```javascript
// 1. Include required assets in Blade
@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-buttons-bs5/buttons.bootstrap5.scss'
])
@endsection

@section('vendor-script')
@vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js'
])
@endsection

@section('page-script')
@vite([
  'resources/js/datatables-config.js',
  'resources/js/your-module.js'
])
@endsection
```

### JavaScript Implementation
```javascript
// 2. Initialize DataTable với configuration helper
$(function () {
  var dt_table = $('.datatables-basic');
  
  if (dt_table.length) {
    // Get default configuration
    var defaultConfig = DataTablesConfig.getDefaultConfig(isDarkStyle, config);
    
    var dt = dt_table.DataTable($.extend(true, {}, defaultConfig, {
      ajax: {
        url: baseUrl + 'module/data-endpoint',
        type: 'GET',
        data: function(d) {
          // Add custom filters
          d.status_filter = $('#StatusFilter').val();
          d.category_filter = $('#CategoryFilter').val();
        }
      },
      columns: [
        { data: '' },           // Responsive control
        { data: 'id' },         // ID (hidden)
        { data: 'name' },       // Name
        { data: 'email' },      // Email
        { data: 'status' },     // Status
        { data: 'created_at' }, // Date
        { data: 'actions' }     // Actions
      ],
      columnDefs: DataTablesConfig.getCommonColumnDefs().concat([
        {
          // Custom column rendering
          targets: 2,
          render: function (data, type, full, meta) {
            return '<strong>' + data + '</strong>';
          }
        }
      ])
    }));

    // Initialize form controls
    DataTablesConfig.initializeFormControls();
  }
});
```

### HTML Structure
```html
<!-- Filters (optional) -->
<div class="row mb-3">
  <div class="col-md-3">
    <select id="StatusFilter" class="form-select">
      <option value="">All Status</option>
      <option value="active">Active</option>
      <option value="inactive">Inactive</option>
    </select>
  </div>
</div>

<!-- DataTable -->
<div class="card">
  <div class="card-datatable table-responsive">
    <table class="datatables-basic table table-bordered">
      <thead>
        <tr>
          <th></th>          <!-- Responsive control -->
          <th>ID</th>        <!-- Hidden column -->
          <th>Name</th>
          <th>Email</th>
          <th>Status</th>
          <th>Created</th>
          <th>Actions</th>
        </tr>
      </thead>
    </table>
  </div>
</div>
```

### Controller Data Endpoint
```php
public function getData(Request $request)
{
    $query = Model::query();

    // Handle custom filters
    if ($request->has('status_filter') && !empty($request->status_filter)) {
        $query->where('status', $request->status_filter);
    }

    // Handle search
    if ($request->has('search') && !empty($request->search['value'])) {
        $searchValue = $request->search['value'];
        $query->where(function ($q) use ($searchValue) {
            $q->where('name', 'like', "%{$searchValue}%")
              ->orWhere('email', 'like', "%{$searchValue}%");
        });
    }

    // Handle ordering
    if ($request->has('order')) {
        $orderColumn = $request->columns[$request->order[0]['column']]['data'];
        $orderDirection = $request->order[0]['dir'];
        $query->orderBy($orderColumn, $orderDirection);
    }

    // Pagination
    $recordsTotal = $query->count();
    $recordsFiltered = $recordsTotal;
    
    $start = $request->start ?? 0;
    $length = $request->length ?? 10;
    
    $data = $query->skip($start)->take($length)->get();

    // Transform data
    $data = $data->map(function ($item) {
        return [
            'id' => $item->id,
            'name' => $item->name,
            'email' => $item->email,
            'status' => '<span class="badge bg-label-' . ($item->status === 'active' ? 'success' : 'secondary') . '">' . ucfirst($item->status) . '</span>',
            'created_at' => $item->created_at->format('d/m/Y'),
            'actions' => view('admin.partials.actions', compact('item'))->render()
        ];
    });

    return response()->json([
        'draw' => $request->draw,
        'recordsTotal' => $recordsTotal,
        'recordsFiltered' => $recordsFiltered,
        'data' => $data
    ]);
}
```

### Features Included
- ✅ Export buttons (CSV, Excel, PDF, Print, Copy)
- ✅ Responsive design với modal details
- ✅ Search và filtering
- ✅ Pagination
- ✅ Dark/Light theme support
- ✅ Bootstrap 5 styling
- ✅ Custom column rendering
- ✅ AJAX data loading

---

## 📝 Form Components

### Select2 Implementation
```javascript
// 1. Include assets
@section('vendor-style')
@vite(['resources/assets/vendor/libs/select2/select2.scss'])
@endsection

@section('vendor-script')
@vite(['resources/assets/vendor/libs/select2/select2.js'])
@endsection

// 2. Initialize Select2
$(function () {
  // Basic Select2
  $('.select2').select2({
    placeholder: 'Choose option...',
    allowClear: true
  });

  // Select2 with AJAX
  $('.select2-ajax').select2({
    ajax: {
      url: baseUrl + 'api/search-endpoint',
      dataType: 'json',
      delay: 250,
      data: function (params) {
        return {
          q: params.term,
          page: params.page
        };
      },
      processResults: function (data, params) {
        return {
          results: data.items,
          pagination: {
            more: data.pagination.more
          }
        };
      }
    },
    placeholder: 'Search...',
    minimumInputLength: 2
  });

  // Apply focus handling for floating labels
  select2Focus('.select2');
});
```

### HTML Structure
```html
<!-- Basic Select2 -->
<div class="form-floating form-floating-outline">
  <select class="form-select select2" id="basicSelect">
    <option value="">Choose...</option>
    <option value="1">Option 1</option>
    <option value="2">Option 2</option>
  </select>
  <label for="basicSelect">Select Option</label>
</div>

<!-- Select2 with AJAX -->
<div class="form-floating form-floating-outline">
  <select class="form-select select2-ajax" id="ajaxSelect">
    <option value="">Search...</option>
  </select>
  <label for="ajaxSelect">Search Items</label>
</div>
```

### Form Validation
```javascript
// Using FormValidation library
const form = document.getElementById('form-id');
const fv = FormValidation.formValidation(form, {
  fields: {
    fieldName: {
      validators: {
        notEmpty: {
          message: 'Field is required'
        },
        emailAddress: {
          message: 'Please enter a valid email'
        }
      }
    }
  },
  plugins: {
    trigger: new FormValidation.plugins.Trigger(),
    bootstrap5: new FormValidation.plugins.Bootstrap5({
      eleValidClass: '',
      rowSelector: '.form-floating, .input-group'
    }),
    submitButton: new FormValidation.plugins.SubmitButton(),
    autoFocus: new FormValidation.plugins.AutoFocus()
  }
});
```

### SweetAlert2 Integration
```javascript
// Success notification
Swal.fire({
  title: 'Success!',
  text: 'Operation completed successfully',
  icon: 'success',
  confirmButtonText: 'OK',
  customClass: {
    confirmButton: 'btn btn-primary'
  }
});

// Confirmation dialog
Swal.fire({
  title: 'Are you sure?',
  text: 'This action cannot be undone',
  icon: 'warning',
  showCancelButton: true,
  confirmButtonText: 'Yes, delete it!',
  cancelButtonText: 'Cancel',
  customClass: {
    confirmButton: 'btn btn-danger me-2',
    cancelButton: 'btn btn-outline-secondary'
  }
}).then((result) => {
  if (result.isConfirmed) {
    // Perform action
  }
});
```

---

## ⚡ Vite Integration

### Configuration Overview
File `vite.config.js` được cấu hình để auto-discover các file JavaScript và CSS:

```javascript
// Auto-discovery patterns
const pageJsFiles = GetFilesArray('resources/assets/js/*.js');
const vendorJsFiles = GetFilesArray('resources/assets/vendor/js/*.js');
const LibsJsFiles = GetFilesArray('resources/assets/vendor/libs/**/*.js');
const customJsFiles = GetFilesArray('resources/js/*.js');

// SCSS patterns
const CoreScssFiles = GetFilesArray('resources/assets/vendor/scss/**/!(_)*.scss');
const LibsScssFiles = GetFilesArray('resources/assets/vendor/libs/**/!(_)*.scss');
const CustomScssFiles = GetFilesArray('resources/css/*.css');
```

### Adding New Assets
1. **Tạo file mới**: Đặt trong thư mục tương ứng (`resources/js/` hoặc `resources/css/`)
2. **Auto-discovery**: Vite sẽ tự động phát hiện file mới
3. **Reference trong Blade**: Sử dụng `@vite()` directive

```php
<!-- Thêm custom CSS -->
@section('vendor-style')
@vite(['resources/css/new-module.css'])
@endsection

<!-- Thêm custom JavaScript -->
@section('page-script')
@vite(['resources/js/new-module.js'])
@endsection
```

### Development Commands
```bash
# Development server với hot reload
npm run dev

# Build cho production
npm run build

# Preview production build
npm run preview
```

### Asset URL Helpers
```javascript
// Global variables available
window.assetsPath  // Path to assets directory
window.baseUrl     // Application base URL
window.config      // Theme configuration object
```

---

## 📚 Templates và Examples

### CRUD Module Template
Tạo module CRUD hoàn chỉnh theo pattern hiện tại:

#### 1. Controller Template
```php
<?php

namespace App\Http\Controllers\Admin;

use App\Models\YourModel;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;

class YourModelController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function index(Request $request)
    {
        return view('admin.your-models.index');
    }

    public function getData(Request $request)
    {
        // DataTables data endpoint implementation
        // (See DataTables section above)
    }

    public function create()
    {
        return view('admin.your-models.create');
    }

    public function store(Request $request)
    {
        // Validation and store logic
    }

    public function show(YourModel $yourModel)
    {
        return view('admin.your-models.show', compact('yourModel'));
    }

    public function edit(YourModel $yourModel)
    {
        return view('admin.your-models.edit', compact('yourModel'));
    }

    public function update(Request $request, YourModel $yourModel)
    {
        // Validation and update logic
    }

    public function destroy(YourModel $yourModel)
    {
        // Delete logic
    }
}
```

#### 2. View Template (Index)
```php
@extends('layouts/layoutMaster')

@section('title', 'Your Model Management')

@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-buttons-bs5/buttons.bootstrap5.scss',
  'resources/assets/vendor/libs/select2/select2.scss',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss'
])
@endsection

@section('vendor-script')
@vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js',
  'resources/assets/vendor/libs/select2/select2.js',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js'
])
@endsection

@section('page-script')
@vite([
  'resources/js/datatables-config.js',
  'resources/js/your-model-management.js'
])
@endsection

@section('content')
<!-- Statistics Cards -->
<div class="row g-6 mb-6">
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div class="me-1">
            <p class="text-heading mb-1">Total Items</p>
            <div class="d-flex align-items-center">
              <h4 class="mb-1 me-2">{{ $totalCount ?? 0 }}</h4>
            </div>
            <small class="mb-0">All items</small>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-primary rounded-3">
              <div class="ri-file-list-line ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- Add more statistics cards as needed -->
</div>

<!-- Filters -->
<div class="card mb-6">
  <div class="card-body">
    <div class="row g-3">
      <div class="col-md-3">
        <div class="form-floating form-floating-outline">
          <select id="StatusFilter" class="form-select">
            <option value="">All Status</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
          </select>
          <label for="StatusFilter">Status</label>
        </div>
      </div>
      <div class="col-md-3">
        <button type="button" class="btn btn-outline-secondary" id="clearFilters">
          <i class="ri-refresh-line me-1"></i>Clear Filters
        </button>
      </div>
    </div>
  </div>
</div>

<!-- DataTable -->
<div class="card">
  <div class="card-header d-flex justify-content-between align-items-center">
    <h5 class="card-title mb-0">Your Models List</h5>
    <a href="{{ route('admin.your-models.create') }}" class="btn btn-primary">
      <i class="ri-add-line me-1"></i>Add New
    </a>
  </div>
  <div class="card-datatable table-responsive">
    <table class="datatables-your-models table table-bordered">
      <thead>
        <tr>
          <th></th>
          <th>ID</th>
          <th>Name</th>
          <th>Status</th>
          <th>Created</th>
          <th>Actions</th>
        </tr>
      </thead>
    </table>
  </div>
</div>
@endsection
```

#### 3. JavaScript Template
```javascript
/**
 * Your Model Management JavaScript
 */

'use strict';

$(function () {
  let borderColor, bodyBg, headingColor;

  if (isDarkStyle) {
    borderColor = config.colors_dark.borderColor;
    bodyBg = config.colors_dark.bodyBg;
    headingColor = config.colors_dark.headingColor;
  } else {
    borderColor = config.colors.borderColor;
    bodyBg = config.colors.bodyBg;
    headingColor = config.colors.headingColor;
  }

  // Variable declaration for table
  var dt_table = $('.datatables-your-models'),
    statusObj = {
      active: { title: 'Active', class: 'bg-label-success' },
      inactive: { title: 'Inactive', class: 'bg-label-secondary' }
    };

  // DataTable initialization
  if (dt_table.length) {
    var defaultConfig = DataTablesConfig.getDefaultConfig(isDarkStyle, config);

    var dt = dt_table.DataTable($.extend(true, {}, defaultConfig, {
      ajax: {
        url: baseUrl + 'admin/your-models-data',
        type: 'GET',
        data: function(d) {
          d.status_filter = $('#StatusFilter').val();
        }
      },
      columns: [
        { data: '' },
        { data: 'id' },
        { data: 'name' },
        { data: 'status_badge' },
        { data: 'created_at' },
        { data: 'actions' }
      ],
      columnDefs: DataTablesConfig.getCommonColumnDefs().concat([
        {
          targets: 2,
          responsivePriority: 1,
          render: function (data, type, full, meta) {
            return '<span class="fw-medium">' + data + '</span>';
          }
        },
        {
          targets: -1,
          title: 'Actions',
          searchable: false,
          orderable: false,
          render: function (data, type, full, meta) {
            return data;
          }
        }
      ])
    }));

    DataTablesConfig.initializeFormControls();
  }

  // Filter functionality
  $('#StatusFilter').on('change', function () {
    dt.ajax.reload();
  });

  $('#clearFilters').on('click', function () {
    $('#StatusFilter').val('').trigger('change');
  });

  // Delete functionality
  $(document).on('click', '.delete-record', function () {
    var id = $(this).data('id');
    var name = $(this).data('name');

    Swal.fire({
      title: 'Are you sure?',
      text: 'You want to delete "' + name + '"?',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, delete it!',
      cancelButtonText: 'Cancel',
      customClass: {
        confirmButton: 'btn btn-danger me-2',
        cancelButton: 'btn btn-outline-secondary'
      }
    }).then((result) => {
      if (result.isConfirmed) {
        // Perform delete request
        $.ajax({
          url: baseUrl + 'admin/your-models/' + id,
          type: 'DELETE',
          headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
          },
          success: function(response) {
            dt.ajax.reload();
            Swal.fire({
              title: 'Deleted!',
              text: 'Record has been deleted.',
              icon: 'success',
              customClass: {
                confirmButton: 'btn btn-success'
              }
            });
          },
          error: function(xhr) {
            Swal.fire({
              title: 'Error!',
              text: 'Something went wrong.',
              icon: 'error',
              customClass: {
                confirmButton: 'btn btn-danger'
              }
            });
          }
        });
      }
    });
  });
});
```

#### 4. Routes Template
```php
// routes/web.php
Route::prefix('admin')->name('admin.')->group(function () {
    Route::resource('your-models', YourModelController::class);
    Route::get('your-models-data', [YourModelController::class, 'getData'])->name('your-models.data');
});
```

#### 5. Model Template
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class YourModel extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'status',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeInactive($query)
    {
        return $query->where('status', 'inactive');
    }

    // Accessors
    public function getStatusBadgeAttribute()
    {
        $class = $this->status === 'active' ? 'success' : 'secondary';
        return '<span class="badge bg-label-' . $class . '">' . ucfirst($this->status) . '</span>';
    }
}
```

---

## 🎯 Best Practices Summary

### ✅ DO's
- Sử dụng `resources/views/template` làm reference cho UI patterns
- Tổ chức JavaScript và CSS trong thư mục riêng biệt
- Sử dụng DataTablesConfig helper cho consistent table styling
- Apply Select2 cho tất cả dropdown selections
- Sử dụng SweetAlert2 cho user notifications
- Follow Bootstrap 5 component structure
- Use Vite để build và optimize assets
- Implement responsive design patterns
- Use semantic HTML5 elements

### ❌ DON'Ts
- Không nhúng JavaScript hoặc CSS trực tiếp trong Blade templates
- Không sử dụng Yajra DataTables package (dùng client-side)
- Không modify vendor library files trực tiếp
- Không hardcode URLs (sử dụng route helpers)
- Không skip CSRF protection
- Không ignore responsive design requirements

### 🔧 Development Workflow
1. **Planning**: Xác định requirements và reference existing patterns
2. **Structure**: Tạo controller, model, views theo template
3. **Assets**: Tạo custom JS/CSS files và add vào Vite config
4. **Testing**: Test functionality trên multiple screen sizes
5. **Optimization**: Review performance và accessibility
6. **Documentation**: Update project documentation

---

## 📞 Support và Resources

### Internal Resources
- **Template Examples**: `resources/views/template/`
- **Existing Modules**: `resources/views/admin/`
- **Asset Libraries**: `resources/assets/vendor/libs/`
- **Configuration**: `resources/assets/js/config.js`

### External Documentation
- [Laravel 11 Documentation](https://laravel.com/docs/11.x)
- [Bootstrap 5 Documentation](https://getbootstrap.com/docs/5.3/)
- [DataTables Documentation](https://datatables.net/)
- [Select2 Documentation](https://select2.org/)
- [Vite Documentation](https://vitejs.dev/)

---

*Tài liệu này được tạo dựa trên phân tích toàn diện dự án VPCC Laravel. Hãy cập nhật khi có thay đổi trong cấu trúc hoặc requirements.*
