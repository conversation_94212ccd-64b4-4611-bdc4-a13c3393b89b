# Hướng dẫn Tiêu chuẩn <PERSON><PERSON> triể<PERSON> - VPCC Project

## 0. Tìm hiểu và khai thác cấu trúc hiện có

### C<PERSON>u trúc Controllers
Dự án sử dụng pattern Resource Controllers với các đặc điểm:
- <PERSON><PERSON> thừa từ `Controller` base class
- Sử dụng permissions checking trong mỗi method
- Implement DataTables server-side processing
- T<PERSON><PERSON> về JSON responses cho AJAX requests

**Ví dụ Controller Pattern:**
```php
class AssetFieldController extends Controller
{
    public function index(Request $request)
    {
        // Check permission
        if (!Auth::user()->hasPermission('asset-fields.view')) {
            abort(403, 'Bạn không có quyền xem danh sách field động');
        }

        if ($request->ajax()) {
            // DataTables server-side processing
            $query = AssetField::query();
            return DataTables::of($query)
                ->addColumn('action', function ($field) {
                    // Action buttons
                })
                ->make(true);
        }

        return view('asset-fields.index', compact('data'));
    }
}
```

### Cấu trúc Models
- Sử dụng Eloquent relationships
- Implement scopes cho filtering
- Cast attributes cho data types
- Fillable properties cho mass assignment

**Ví dụ Model Pattern:**
```php
class User extends Model
{
    protected $fillable = ['name', 'email', 'password', 'role_id', 'status'];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
    ];

    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function role(): BelongsTo
    {
        return $this->belongsTo(Role::class);
    }
}
```

## 1. Mẫu HTML và Template Structure

### Cấu trúc Views
Dự án sử dụng cấu trúc template có sẵn trong `resources/views/template/`:

```
resources/views/
├── layouts/
│   └── layoutMaster.blade.php (Main layout)
├── asset-fields/
│   └── index.blade.php
├── asset-templates/
│   └── index.blade.php
├── contract-types/
│   └── index.blade.php
└── template/ (Reference templates)
```

### Template Pattern cho CRUD Views
Mỗi module CRUD phải tuân theo pattern sau:

```blade
@extends('layouts/layoutMaster')

@section('title', 'Tên Module')

@section('vendor-style')
@vite(['resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss'])
@endsection

@section('page-style')
@vite(['resources/css/module-name.css'])
@endsection

@section('vendor-script')
@vite(['resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js'])
@endsection

@section('page-script')
@vite(['resources/js/module-name.js'])
@endsection

@section('content')
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header border-bottom">
        <h5 class="card-title mb-0">
          <i class="ri-icon-name me-2"></i>
          Tên Module
        </h5>
        <div class="d-flex justify-content-between align-items-center row pt-4 gap-4 gap-md-0">
          <div class="col-md-4 col-12">
            @can('permission.create')
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#moduleModal">
              <i class="ri-add-line me-1"></i>
              Thêm mới
            </button>
            @endcan
          </div>
        </div>
      </div>

      <!-- DataTable -->
      <div class="card-datatable table-responsive">
        <table id="moduleTable" class="datatables-module table">
          <thead>
            <tr>
              <th>Column 1</th>
              <th>Column 2</th>
              <th>Thao tác</th>
            </tr>
          </thead>
        </table>
      </div>
    </div>
  </div>
</div>

<!-- Modal for Create/Edit -->
<div class="modal fade" id="moduleModal" tabindex="-1" aria-hidden="true">
  <!-- Modal content -->
</div>
@endsection

@push('scripts')
<script>
// Pass Laravel routes to JavaScript
window.moduleRoutes = {
  index: '{{ route("module.index") }}',
  store: '{{ route("module.store") }}',
  show: function(id) { return `/module/${id}`; },
  update: function(id) { return `/module/${id}`; },
  destroy: function(id) { return `/module/${id}`; }
};
</script>
@endpush
```

## 2. Tổ chức tài nguyên (Assets Organization)

### Cấu trúc thư mục bắt buộc:
```
resources/
├── js/
│   ├── module-name.js (Custom JavaScript cho từng module)
│   ├── app.js (Main application JS)
│   └── bootstrap.js
├── css/
│   ├── module-name.css (Custom CSS cho từng module)
│   └── app.css
└── assets/ (Vendor assets - không chỉnh sửa)
    ├── vendor/
    └── js/
```

### Quy tắc đặt tên files:
- JavaScript: `kebab-case.js` (vd: `asset-fields.js`)
- CSS: `kebab-case.css` (vd: `asset-fields.css`)
- Views: `kebab-case.blade.php`

### Cấm tuyệt đối:
❌ **KHÔNG được nhúng JavaScript hoặc CSS trực tiếp trong blade files**
❌ **KHÔNG được chỉnh sửa files trong thư mục `resources/assets/vendor/`**

### Cách tham chiếu assets trong Blade:
```blade
<!-- CSS -->
@section('page-style')
@vite(['resources/css/module-name.css'])
@endsection

<!-- JavaScript -->
@section('page-script')
@vite(['resources/js/module-name.js'])
@endsection
```

## 3. Triển khai JavaScript/CSS

### Cấu hình Vite
File `vite.config.js` đã được cấu hình để build các custom files:

```javascript
export default defineConfig({
  plugins: [
    laravel({
      input: [
        // Custom CSS files for CRUD modules
        'resources/css/contract-types.css',
        'resources/css/asset-fields.css',
        'resources/css/asset-templates.css',
        // Custom JS files for CRUD modules
        'resources/js/contract-types.js',
        'resources/js/asset-fields.js',
        'resources/js/asset-templates.js',
        // ... other files
      ],
      refresh: true
    })
  ]
});
```

### JavaScript Pattern cho CRUD modules:
Mỗi module phải tuân theo pattern sau trong `resources/js/module-name.js`:

```javascript
/**
 * Module Name Management JavaScript
 * Handles CRUD operations with AJAX
 */

// Global variables
let moduleTable;
let deleteModuleId = null;
let isEditMode = false;

// Initialize when DOM is ready
$(document).ready(function() {
  initializeDataTable();
  initializeEventHandlers();
});

/**
 * Initialize DataTable
 */
function initializeDataTable() {
  moduleTable = $('#moduleTable').DataTable({
    processing: true,
    serverSide: true,
    ajax: {
      url: window.moduleRoutes.index,
      error: function(xhr, error, thrown) {
        console.error('DataTable AJAX error:', error);
        showToast('error', 'Có lỗi xảy ra khi tải dữ liệu');
      }
    },
    columns: [
      { data: 'name', name: 'name' },
      { data: 'action', name: 'action', orderable: false, searchable: false }
    ],
    language: {
      url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/vi.json'
    },
    responsive: true,
    pageLength: 25,
    lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]]
  });
}

// Export functions to global scope for DataTable buttons
window.editModule = editModule;
window.deleteModule = deleteModule;
```

### CSS Pattern:
Tạo file CSS riêng cho mỗi module với các custom styles cần thiết:

```css
/* resources/css/module-name.css */

/* Module specific styles */
.module-specific-class {
  /* Custom styles */
}

/* DataTable customizations */
.datatables-module .btn {
  margin: 0 2px;
}

/* Modal customizations */
#moduleModal .modal-body {
  /* Custom modal styles */
}
```

## 4. Build Assets
Sau khi tạo hoặc chỉnh sửa JS/CSS files, phải chạy build:

```bash
# Development build
npm run dev

# Production build
npm run build

# Watch mode (auto-rebuild khi có thay đổi)
npm run dev -- --watch
```

## 4. Triển khai DataTables

### Yêu cầu bắt buộc:
- **Tất cả danh sách phải sử dụng DataTables library**
- **Sử dụng server-side processing cho hiệu suất**
- **Tích hợp Bootstrap 5 styling**
- **Hỗ trợ export (CSV, Excel, PDF, Print)**

### DataTables Implementation Pattern:

#### 1. Controller Implementation:
```php
public function index(Request $request)
{
    if ($request->ajax()) {
        $query = Model::query();

        return DataTables::of($query)
            ->addColumn('action', function ($item) {
                $editBtn = '<button type="button" class="btn btn-sm btn-icon btn-outline-primary" onclick="editItem(' . $item->id . ')" title="Sửa">
                    <i class="ri-edit-line"></i>
                </button>';

                $deleteBtn = '<button type="button" class="btn btn-sm btn-icon btn-outline-danger" onclick="deleteItem(' . $item->id . ')" title="Xóa">
                    <i class="ri-delete-bin-line"></i>
                </button>';

                return $editBtn . ' ' . $deleteBtn;
            })
            ->addColumn('status_badge', function ($item) {
                $class = $item->is_active ? 'bg-label-success' : 'bg-label-secondary';
                $text = $item->is_active ? 'Hoạt động' : 'Không hoạt động';
                return '<span class="badge ' . $class . '">' . $text . '</span>';
            })
            ->rawColumns(['action', 'status_badge'])
            ->make(true);
    }

    return view('module.index');
}
```

#### 2. JavaScript DataTable Configuration:
```javascript
function initializeDataTable() {
  moduleTable = $('#moduleTable').DataTable({
    processing: true,
    serverSide: true,
    ajax: {
      url: window.moduleRoutes.index,
      error: function(xhr, error, thrown) {
        console.error('DataTable AJAX error:', error);
        showToast('error', 'Có lỗi xảy ra khi tải dữ liệu');
      }
    },
    columns: [
      { data: 'name', name: 'name' },
      { data: 'status_badge', name: 'is_active', orderable: false, className: 'text-center' },
      { data: 'action', name: 'action', orderable: false, searchable: false, className: 'text-center' }
    ],
    order: [[0, 'asc']],
    language: {
      url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/vi.json'
    },
    responsive: true,
    pageLength: 25,
    lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
    dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>rtip',
    drawCallback: function(settings) {
      // Add fade-in animation to new rows
      $('#moduleTable tbody tr').addClass('fade-in');
    }
  });
}
```

#### 3. Vendor Assets cho DataTables:
```blade
@section('vendor-style')
@vite(['resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss'])
@endsection

@section('vendor-script')
@vite(['resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js'])
@endsection
```

### Filtering và Search:
DataTables đã tích hợp sẵn:
- Global search box
- Column-specific filtering
- Sorting cho tất cả columns (trừ action column)
- Pagination với options 10, 25, 50, 100 records

## 5. Thành phần biểu mẫu (Form Components)

### Select2 Implementation:
**Tất cả dropdown select phải sử dụng Select2 library**

#### 1. Basic Select2:
```html
<select id="selectField" name="field_name" class="form-select select2" required>
  <option value="">Chọn tùy chọn</option>
  @foreach($options as $key => $value)
    <option value="{{ $key }}">{{ $value }}</option>
  @endforeach
</select>
```

#### 2. JavaScript Initialization:
```javascript
// Initialize Select2
$('.select2').select2({
  placeholder: 'Chọn tùy chọn',
  allowClear: true,
  width: '100%'
});
```

#### 3. Dynamic Data Loading với AJAX:
```javascript
$('#dynamicSelect').select2({
  ajax: {
    url: '/api/search-endpoint',
    dataType: 'json',
    delay: 250,
    data: function (params) {
      return {
        q: params.term,
        page: params.page
      };
    },
    processResults: function (data, params) {
      params.page = params.page || 1;
      return {
        results: data.items,
        pagination: {
          more: (params.page * 30) < data.total_count
        }
      };
    },
    cache: true
  },
  placeholder: 'Tìm kiếm...',
  minimumInputLength: 2
});
```

#### 4. Vendor Assets cho Select2:
```blade
@section('vendor-style')
@vite(['resources/assets/vendor/libs/select2/select2.scss'])
@endsection

@section('vendor-script')
@vite(['resources/assets/vendor/libs/select2/select2.js'])
@endsection
```

### Form Validation Pattern:
```javascript
// Form validation
$('#moduleForm').on('submit', function(e) {
  e.preventDefault();

  // Clear previous validation errors
  clearValidationErrors();

  const formData = new FormData(this);
  const data = Object.fromEntries(formData.entries());

  $.ajax({
    url: isEditMode ? window.moduleRoutes.update(data.id) : window.moduleRoutes.store,
    type: isEditMode ? 'PUT' : 'POST',
    data: data,
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    },
    success: function(response) {
      if (response.success) {
        $('#moduleModal').modal('hide');
        moduleTable.ajax.reload(null, false);
        showToast('success', response.message);
      }
    },
    error: function(xhr) {
      if (xhr.status === 422) {
        // Validation errors
        const errors = xhr.responseJSON.errors;
        displayValidationErrors(errors);
      } else {
        showToast('error', 'Có lỗi xảy ra');
      }
    }
  });
});
```

## 6. Menu và Navigation

### Tạo menu tương ứng với chức năng:
Thêm menu items vào layout chính trong `resources/views/layouts/sections/menu/verticalMenu.blade.php`:

```blade
<!-- Document Management -->
<li class="menu-header small text-uppercase">
  <span class="menu-header-text">Quản lý tài liệu</span>
</li>

<li class="menu-item {{ request()->routeIs('contract-types.*') ? 'active' : '' }}">
  <a href="{{ route('contract-types.index') }}" class="menu-link">
    <i class="menu-icon tf-icons ri-file-list-line"></i>
    <div>Loại hợp đồng</div>
  </a>
</li>

<li class="menu-item {{ request()->routeIs('asset-fields.*') ? 'active' : '' }}">
  <a href="{{ route('asset-fields.index') }}" class="menu-link">
    <i class="menu-icon tf-icons ri-input-field"></i>
    <div>Field động</div>
  </a>
</li>

<li class="menu-item {{ request()->routeIs('asset-templates.*') ? 'active' : '' }}">
  <a href="{{ route('asset-templates.index') }}" class="menu-link">
    <i class="menu-icon tf-icons ri-file-copy-line"></i>
    <div>Template</div>
  </a>
</li>
```

### Nhóm menu theo chức năng:
- **Quản lý tài liệu**: Contract Types, Asset Fields, Asset Templates
- **Quản lý người dùng**: Users, Roles, Permissions
- **Báo cáo**: Reports, Analytics

## 7. Permissions và Security

### Permission Naming Convention:
```
{module-name}.{action}
```

Ví dụ:
- `contract-types.view`
- `contract-types.create`
- `contract-types.edit`
- `contract-types.delete`

### Sử dụng Permissions trong Views:
```blade
@can('contract-types.create')
<button type="button" class="btn btn-primary" data-bs-toggle="modal">
  <i class="ri-add-line me-1"></i>
  Thêm loại hợp đồng
</button>
@endcan
```

### Sử dụng Permissions trong Controllers:
```php
public function index(Request $request)
{
    if (!Auth::user()->hasPermission('contract-types.view')) {
        abort(403, 'Bạn không có quyền xem danh sách loại hợp đồng');
    }

    // Controller logic
}
```

## 8. Database và Migration

### Migration Pattern:
```php
Schema::create('table_name', function (Blueprint $table) {
    $table->id();
    $table->string('name');
    $table->text('description')->nullable();
    $table->boolean('is_active')->default(true);
    $table->integer('sort_order')->default(0);
    $table->timestamps();
});
```

### Seeder Pattern:
```php
class ModuleSeeder extends Seeder
{
    public function run(): void
    {
        $items = [
            [
                'name' => 'Item 1',
                'description' => 'Description 1',
                'is_active' => true,
                'sort_order' => 1,
            ],
        ];

        foreach ($items as $item) {
            Model::firstOrCreate(['name' => $item['name']], $item);
        }
    }
}
```

## 9. Testing và Debugging

### Chạy tests:
```bash
# Chạy tất cả tests
php artisan test

# Chạy tests cho module cụ thể
php artisan test --filter=ContractTypeTest
```

### Debug DataTables:
1. Kiểm tra Network tab trong Developer Tools
2. Xem AJAX requests và responses
3. Kiểm tra console errors
4. Verify routes và permissions

### Common Issues và Solutions:

#### 1. DataTables không load dữ liệu:
- Kiểm tra route có đúng không
- Kiểm tra permissions
- Kiểm tra AJAX response format

#### 2. Assets không load:
- Chạy `npm run build`
- Kiểm tra Vite configuration
- Clear browser cache

#### 3. Permissions không hoạt động:
- Kiểm tra user có role không
- Kiểm tra role có permissions không
- Kiểm tra permission names có đúng không

## 10. Deployment Checklist

### Trước khi deploy:
- [ ] Chạy `npm run build` để build production assets
- [ ] Chạy `php artisan config:cache`
- [ ] Chạy `php artisan route:cache`
- [ ] Chạy `php artisan view:cache`
- [ ] Chạy migrations: `php artisan migrate`
- [ ] Chạy seeders: `php artisan db:seed`
- [ ] Test tất cả chức năng CRUD
- [ ] Test permissions cho các roles khác nhau

### Sau khi deploy:
- [ ] Kiểm tra logs: `php artisan log:clear`
- [ ] Test performance DataTables với dữ liệu lớn
- [ ] Verify tất cả assets load đúng
- [ ] Test responsive design trên mobile

---

## Kết luận

Dự án VPCC đã có cấu trúc tốt và tuân theo các best practices của Laravel. Các vấn đề đã được khắc phục:

1. ✅ Lỗi duplicate method đã được sửa
2. ✅ Assets đã được build thành công
3. ✅ Database có đầy đủ dữ liệu test
4. ✅ User đã có permissions đầy đủ
5. ✅ Tất cả CRUD modules đã có nút Create

Hệ thống hiện tại đã sẵn sàng để phát triển thêm các modules mới theo các tiêu chuẩn đã được thiết lập.
