# DataTables Implementation Guide - Hướng Dẫn Triển Khai DataTables

## 📋 Tổng Quan

Dự án VPCC sử dụng **client-side DataTables** vớ<PERSON> Bootstrap 5 styling thay vì Yajra server-side package. <PERSON><PERSON><PERSON><PERSON> này cho phép:
- <PERSON><PERSON><PERSON> đ<PERSON> load nhanh hơn với datasets nhỏ-trung bình
- Flexibility cao hơn trong customization
- Tương thích tốt với theme hiện tại
- Export functionality built-in

---

## 🛠️ Setup Cơ Bản

### 1. Assets Required

#### Blade Template
```php
@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-buttons-bs5/buttons.bootstrap5.scss'
])
@endsection

@section('vendor-script')
@vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js'
])
@endsection

@section('page-script')
@vite([
  'resources/js/datatables-config.js',
  'resources/js/your-module.js'
])
@endsection
```

### 2. HTML Structure

```html
<!-- Filters (Optional) -->
<div class="card mb-6">
  <div class="card-body">
    <div class="row g-3">
      <div class="col-md-3">
        <div class="form-floating form-floating-outline">
          <select id="StatusFilter" class="form-select">
            <option value="">All Status</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
          </select>
          <label for="StatusFilter">Status</label>
        </div>
      </div>
      <div class="col-md-3">
        <button type="button" class="btn btn-outline-secondary" id="clearFilters">
          <i class="ri-refresh-line me-1"></i>Clear Filters
        </button>
      </div>
    </div>
  </div>
</div>

<!-- DataTable -->
<div class="card">
  <div class="card-header d-flex justify-content-between align-items-center">
    <h5 class="card-title mb-0">Data List</h5>
    <button class="btn btn-primary" id="addNewRecord">
      <i class="ri-add-line me-1"></i>Add New
    </button>
  </div>
  <div class="card-datatable table-responsive">
    <table class="datatables-basic table table-bordered">
      <thead>
        <tr>
          <th></th>          <!-- Responsive control -->
          <th>ID</th>        <!-- Hidden column -->
          <th>Name</th>
          <th>Email</th>
          <th>Status</th>
          <th>Created</th>
          <th>Actions</th>
        </tr>
      </thead>
    </table>
  </div>
</div>
```

---

## 🔧 JavaScript Implementation

### 1. Basic Configuration

```javascript
'use strict';

$(function () {
  // Theme variables
  let borderColor, bodyBg, headingColor;

  if (isDarkStyle) {
    borderColor = config.colors_dark.borderColor;
    bodyBg = config.colors_dark.bodyBg;
    headingColor = config.colors_dark.headingColor;
  } else {
    borderColor = config.colors.borderColor;
    bodyBg = config.colors.bodyBg;
    headingColor = config.colors.headingColor;
  }

  // Table variable
  var dt_table = $('.datatables-basic');

  // Initialize DataTable
  if (dt_table.length) {
    var defaultConfig = DataTablesConfig.getDefaultConfig(isDarkStyle, config);

    var dt = dt_table.DataTable($.extend(true, {}, defaultConfig, {
      ajax: {
        url: baseUrl + 'admin/data-endpoint',
        type: 'GET',
        data: function(d) {
          // Add custom filters
          d.status_filter = $('#StatusFilter').val();
          d.category_filter = $('#CategoryFilter').val();
        }
      },
      columns: [
        { data: '' },           // Responsive control
        { data: 'id' },         // ID (hidden)
        { data: 'name' },       // Name
        { data: 'email' },      // Email
        { data: 'status' },     // Status
        { data: 'created_at' }, // Date
        { data: 'actions' }     // Actions
      ],
      columnDefs: DataTablesConfig.getCommonColumnDefs().concat([
        {
          // Name column with custom rendering
          targets: 2,
          responsivePriority: 1,
          render: function (data, type, full, meta) {
            return '<span class="fw-medium">' + data + '</span>';
          }
        },
        {
          // Status column with badges
          targets: 4,
          render: function (data, type, full, meta) {
            var statusObj = {
              'active': { title: 'Active', class: 'bg-label-success' },
              'inactive': { title: 'Inactive', class: 'bg-label-secondary' }
            };
            return '<span class="badge ' + statusObj[data].class + '">' + statusObj[data].title + '</span>';
          }
        },
        {
          // Actions column
          targets: -1,
          title: 'Actions',
          searchable: false,
          orderable: false,
          render: function (data, type, full, meta) {
            return data;
          }
        }
      ])
    }));

    // Initialize form controls
    DataTablesConfig.initializeFormControls();
  }

  // Filter functionality
  $('#StatusFilter').on('change', function () {
    dt.ajax.reload();
  });

  $('#clearFilters').on('click', function () {
    $('#StatusFilter').val('').trigger('change');
  });
});
```

### 2. Advanced Features

#### Custom Column Rendering
```javascript
columnDefs: [
  {
    // Avatar + Name column
    targets: 2,
    render: function (data, type, full, meta) {
      var $name = full['name'];
      var $email = full['email'];
      var $avatar = full['avatar'];
      
      if ($avatar) {
        var $output = '<img src="' + assetsPath + 'img/avatars/' + $avatar + '" alt="Avatar" class="rounded-circle">';
      } else {
        var stateNum = Math.floor(Math.random() * 6);
        var states = ['success', 'danger', 'warning', 'info', 'dark', 'primary'];
        var $state = states[stateNum];
        var $initials = $name.match(/\b\w/g) || [];
        $initials = (($initials.shift() || '') + ($initials.pop() || '')).toUpperCase();
        $output = '<span class="avatar-initial rounded-circle bg-label-' + $state + '">' + $initials + '</span>';
      }
      
      return '<div class="d-flex justify-content-start align-items-center">' +
             '<div class="avatar-wrapper">' +
             '<div class="avatar avatar-sm me-3">' + $output + '</div>' +
             '</div>' +
             '<div class="d-flex flex-column">' +
             '<span class="fw-medium">' + $name + '</span>' +
             '<small>' + $email + '</small>' +
             '</div>' +
             '</div>';
    }
  }
]
```

#### Progress Bar Column
```javascript
{
  targets: 3,
  render: function (data, type, full, meta) {
    var $progress = data;
    var $progress_color = 'primary';
    
    if ($progress > 75) {
      $progress_color = 'success';
    } else if ($progress > 50) {
      $progress_color = 'info';
    } else if ($progress > 25) {
      $progress_color = 'warning';
    } else {
      $progress_color = 'danger';
    }
    
    return '<div class="d-flex align-items-center">' +
           '<div class="progress w-100 me-3" style="height: 6px;">' +
           '<div class="progress-bar bg-' + $progress_color + '" style="width: ' + $progress + '%"></div>' +
           '</div>' +
           '<span class="text-muted">' + $progress + '%</span>' +
           '</div>';
  }
}
```

#### Custom Action Buttons
```javascript
{
  targets: -1,
  render: function (data, type, full, meta) {
    return '<div class="d-flex align-items-center gap-2">' +
           '<button class="btn btn-sm btn-outline-info view-record" data-id="' + full.id + '" title="View">' +
           '<i class="ri-eye-line"></i>' +
           '</button>' +
           '<button class="btn btn-sm btn-outline-primary edit-record" data-id="' + full.id + '" title="Edit">' +
           '<i class="ri-edit-line"></i>' +
           '</button>' +
           '<button class="btn btn-sm btn-outline-danger delete-record" data-id="' + full.id + '" data-name="' + full.name + '" title="Delete">' +
           '<i class="ri-delete-bin-line"></i>' +
           '</button>' +
           '</div>';
  }
}
```

---

## 🎛️ Controller Implementation

### 1. Basic Data Endpoint

```php
public function getData(Request $request)
{
    $query = YourModel::query();

    // Handle custom filters
    if ($request->has('status_filter') && !empty($request->status_filter)) {
        $query->where('status', $request->status_filter);
    }

    if ($request->has('category_filter') && !empty($request->category_filter)) {
        $query->where('category_id', $request->category_filter);
    }

    // Handle search
    if ($request->has('search') && !empty($request->search['value'])) {
        $searchValue = $request->search['value'];
        $query->where(function ($q) use ($searchValue) {
            $q->where('name', 'like', "%{$searchValue}%")
              ->orWhere('email', 'like', "%{$searchValue}%")
              ->orWhere('description', 'like', "%{$searchValue}%");
        });
    }

    // Handle ordering
    if ($request->has('order')) {
        $orderColumn = $request->columns[$request->order[0]['column']]['data'];
        $orderDirection = $request->order[0]['dir'];
        
        // Handle relationship ordering
        if ($orderColumn === 'category_name') {
            $query->join('categories', 'your_models.category_id', '=', 'categories.id')
                  ->orderBy('categories.name', $orderDirection)
                  ->select('your_models.*');
        } else {
            $query->orderBy($orderColumn, $orderDirection);
        }
    } else {
        $query->orderBy('created_at', 'desc');
    }

    // Get total records
    $recordsTotal = YourModel::count();
    $recordsFiltered = $query->count();
    
    // Pagination
    $start = $request->start ?? 0;
    $length = $request->length ?? 10;
    
    $data = $query->skip($start)->take($length)->get();

    // Transform data
    $data = $data->map(function ($item) {
        return [
            'id' => $item->id,
            'name' => $item->name,
            'email' => $item->email,
            'avatar' => $item->avatar,
            'status' => $item->status,
            'category_name' => $item->category ? $item->category->name : '-',
            'progress' => rand(10, 100), // Example progress
            'created_at' => $item->created_at->format('d/m/Y'),
            'actions' => view('admin.partials.actions', compact('item'))->render()
        ];
    });

    return response()->json([
        'draw' => $request->draw,
        'recordsTotal' => $recordsTotal,
        'recordsFiltered' => $recordsFiltered,
        'data' => $data
    ]);
}
```

### 2. Advanced Filtering

```php
public function getData(Request $request)
{
    $query = YourModel::with(['category', 'user']);

    // Date range filter
    if ($request->has('date_from') && !empty($request->date_from)) {
        $query->whereDate('created_at', '>=', $request->date_from);
    }
    
    if ($request->has('date_to') && !empty($request->date_to)) {
        $query->whereDate('created_at', '<=', $request->date_to);
    }

    // Multiple status filter
    if ($request->has('status_filter') && !empty($request->status_filter)) {
        if (is_array($request->status_filter)) {
            $query->whereIn('status', $request->status_filter);
        } else {
            $query->where('status', $request->status_filter);
        }
    }

    // Numeric range filter
    if ($request->has('price_min') && !empty($request->price_min)) {
        $query->where('price', '>=', $request->price_min);
    }
    
    if ($request->has('price_max') && !empty($request->price_max)) {
        $query->where('price', '<=', $request->price_max);
    }

    // Relationship filter
    if ($request->has('user_filter') && !empty($request->user_filter)) {
        $query->whereHas('user', function ($userQuery) use ($request) {
            $userQuery->where('name', 'like', '%' . $request->user_filter . '%');
        });
    }

    // Rest of the method...
}
```

---

## 🎨 Advanced Customization

### 1. Custom Export Configuration

```javascript
// Custom export buttons
buttons: [
  {
    extend: 'collection',
    className: 'btn btn-outline-secondary dropdown-toggle mx-3',
    text: '<i class="ri-export-line me-1"></i>Export',
    buttons: [
      {
        extend: 'excel',
        text: '<i class="ri-file-excel-line me-1"></i>Excel',
        className: 'dropdown-item',
        exportOptions: {
          columns: [1, 2, 3, 4, 5], // Specify columns to export
          format: {
            body: function (inner, coldex, rowdex) {
              // Clean HTML from exported data
              if (inner.length <= 0) return inner;
              var el = $.parseHTML(inner);
              var result = '';
              $.each(el, function (index, item) {
                if (item.classList && item.classList.contains('badge')) {
                  result = result + item.textContent;
                } else if (item.innerText === undefined) {
                  result = result + item.textContent;
                } else {
                  result = result + item.innerText;
                }
              });
              return result;
            }
          }
        }
      }
    ]
  }
]
```

### 2. Row Grouping

```javascript
var dt = dt_table.DataTable($.extend(true, {}, defaultConfig, {
  // ... other config
  rowGroup: {
    dataSrc: 'category_name',
    startRender: function (rows, group) {
      return '<strong>' + group + '</strong> (' + rows.count() + ' items)';
    }
  },
  order: [[4, 'asc']] // Order by category first
}));
```

### 3. Fixed Columns

```javascript
var dt = dt_table.DataTable($.extend(true, {}, defaultConfig, {
  // ... other config
  scrollX: true,
  fixedColumns: {
    leftColumns: 2, // Fix first 2 columns
    rightColumns: 1 // Fix last column (actions)
  }
}));
```

### 4. Child Row Details

```javascript
// Add click event for child rows
$('#yourTable tbody').on('click', 'td.details-control', function () {
  var tr = $(this).closest('tr');
  var row = dt.row(tr);

  if (row.child.isShown()) {
    row.child.hide();
    tr.removeClass('shown');
  } else {
    row.child(formatChildRow(row.data())).show();
    tr.addClass('shown');
  }
});

function formatChildRow(data) {
  return '<div class="row">' +
         '<div class="col-md-6">' +
         '<strong>Description:</strong> ' + (data.description || 'N/A') +
         '</div>' +
         '<div class="col-md-6">' +
         '<strong>Notes:</strong> ' + (data.notes || 'N/A') +
         '</div>' +
         '</div>';
}
```

---

## 🔍 Filtering Examples

### 1. Date Range Filter

```html
<!-- HTML -->
<div class="col-md-3">
  <div class="form-floating form-floating-outline">
    <input type="text" class="form-control flatpickr-range" id="dateRange" placeholder="Select date range">
    <label for="dateRange">Date Range</label>
  </div>
</div>
```

```javascript
// JavaScript
$('.flatpickr-range').flatpickr({
  mode: 'range',
  dateFormat: 'Y-m-d',
  onChange: function(selectedDates, dateStr, instance) {
    if (selectedDates.length === 2) {
      dt.ajax.reload();
    }
  }
});

// In AJAX data function
data: function(d) {
  var dateRange = $('#dateRange').val();
  if (dateRange) {
    var dates = dateRange.split(' to ');
    d.date_from = dates[0];
    d.date_to = dates[1] || dates[0];
  }
}
```

### 2. Multi-Select Filter

```html
<!-- HTML -->
<div class="col-md-3">
  <div class="form-floating form-floating-outline">
    <select id="StatusMultiFilter" class="form-select select2" multiple>
      <option value="active">Active</option>
      <option value="inactive">Inactive</option>
      <option value="pending">Pending</option>
    </select>
    <label for="StatusMultiFilter">Status</label>
  </div>
</div>
```

```javascript
// JavaScript
$('#StatusMultiFilter').select2({
  placeholder: 'Select status...',
  allowClear: true
}).on('change', function() {
  dt.ajax.reload();
});

// In AJAX data function
data: function(d) {
  d.status_filter = $('#StatusMultiFilter').val();
}
```

### 3. Search in Specific Columns

```javascript
// Add individual column search
$('#yourTable thead tr').clone(true).appendTo('#yourTable thead');
$('#yourTable thead tr:eq(1) th').each(function (i) {
  var title = $(this).text();
  if (i > 1 && i < 6) { // Only for specific columns
    $(this).html('<input type="text" class="form-control form-control-sm" placeholder="Search ' + title + '" />');
  } else {
    $(this).html('');
  }
});

// Apply the search
dt.columns().every(function () {
  var that = this;
  $('input', this.header()).on('keyup change clear', function () {
    if (that.search() !== this.value) {
      that.search(this.value).draw();
    }
  });
});
```

---

## 🎯 Performance Tips

### 1. Optimize Data Loading
```javascript
// Use server-side processing for large datasets
processing: true,
serverSide: true,
deferRender: true, // Only render visible rows
```

### 2. Limit Initial Load
```javascript
// Set reasonable page length
pageLength: 25,
lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]]
```

### 3. Optimize Columns
```javascript
// Disable sorting/searching for action columns
{
  targets: -1,
  orderable: false,
  searchable: false
}
```

---

## 🐛 Common Issues & Solutions

### 1. Theme Styling Issues
```javascript
// Ensure proper theme variables are loaded
if (typeof config === 'undefined') {
  console.error('Config object not found. Make sure config.js is loaded.');
}
```

### 2. Responsive Issues
```javascript
// Force responsive recalculation
$(window).on('resize', function() {
  dt.columns.adjust().responsive.recalc();
});
```

### 3. Export Issues
```javascript
// Ensure JSZip and pdfMake are loaded
if (typeof JSZip === 'undefined') {
  console.error('JSZip not loaded. Export functionality may not work.');
}
```

---

## 📚 Reference Links

- [DataTables Documentation](https://datatables.net/)
- [Bootstrap 5 Integration](https://datatables.net/examples/styling/bootstrap5)
- [Responsive Extension](https://datatables.net/extensions/responsive/)
- [Buttons Extension](https://datatables.net/extensions/buttons/)

---

*Hướng dẫn này cung cấp foundation hoàn chỉnh cho việc implement DataTables trong dự án VPCC với client-side processing và Bootstrap 5 styling.*
