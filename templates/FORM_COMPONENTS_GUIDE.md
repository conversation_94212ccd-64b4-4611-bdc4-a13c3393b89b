# Form Components Guide - Hướng Dẫn Thành Phần Biểu Mẫu

## 📋 Tổng Quan

Dự án VPCC sử dụng các thành phần form hiện đại với Bootstrap 5, Select2, Form Validation và SweetAlert2. Tất cả components đều được tối ưu cho responsive design và dark/light theme.

---

## 🎯 Select2 Implementation

### 1. Basic Setup

#### Assets Required
```php
@section('vendor-style')
@vite(['resources/assets/vendor/libs/select2/select2.scss'])
@endsection

@section('vendor-script')
@vite(['resources/assets/vendor/libs/select2/select2.js'])
@endsection
```

#### HTML Structure
```html
<!-- Basic Select2 -->
<div class="form-floating form-floating-outline">
  <select class="form-select select2" id="basicSelect" name="basic_select">
    <option value="">Choose option...</option>
    <option value="1">Option 1</option>
    <option value="2">Option 2</option>
    <option value="3">Option 3</option>
  </select>
  <label for="basicSelect">Select Option</label>
</div>

<!-- Multiple Select -->
<div class="form-floating form-floating-outline">
  <select class="form-select select2" id="multiSelect" name="multi_select[]" multiple>
    <option value="1">Option 1</option>
    <option value="2">Option 2</option>
    <option value="3">Option 3</option>
  </select>
  <label for="multiSelect">Multiple Select</label>
</div>
```

#### JavaScript Initialization
```javascript
$(function () {
  // Basic Select2
  $('.select2').select2({
    placeholder: 'Choose option...',
    allowClear: true,
    width: '100%'
  });

  // Multiple Select2
  $('.select2[multiple]').select2({
    placeholder: 'Choose options...',
    allowClear: true,
    width: '100%',
    closeOnSelect: false
  });

  // Apply focus handling for floating labels
  select2Focus('.select2');
});
```

### 2. AJAX Select2

#### HTML
```html
<div class="form-floating form-floating-outline">
  <select class="form-select select2-ajax" id="ajaxSelect" name="ajax_select">
    <option value="">Search...</option>
  </select>
  <label for="ajaxSelect">Search Items</label>
</div>
```

#### JavaScript
```javascript
$('.select2-ajax').select2({
  ajax: {
    url: baseUrl + 'api/search-endpoint',
    dataType: 'json',
    delay: 250,
    data: function (params) {
      return {
        q: params.term,
        page: params.page || 1,
        per_page: 20
      };
    },
    processResults: function (data, params) {
      params.page = params.page || 1;
      
      return {
        results: data.items.map(function(item) {
          return {
            id: item.id,
            text: item.name,
            // Additional data
            email: item.email,
            avatar: item.avatar
          };
        }),
        pagination: {
          more: data.pagination.has_more
        }
      };
    },
    cache: true
  },
  placeholder: 'Search...',
  minimumInputLength: 2,
  templateResult: formatResult,
  templateSelection: formatSelection
});

// Custom result template
function formatResult(item) {
  if (item.loading) {
    return item.text;
  }
  
  var $container = $(
    '<div class="d-flex align-items-center">' +
    '<div class="avatar avatar-sm me-2">' +
    '<span class="avatar-initial rounded-circle bg-label-primary">' + 
    (item.text ? item.text.charAt(0).toUpperCase() : '') + 
    '</span>' +
    '</div>' +
    '<div>' +
    '<div class="fw-medium">' + item.text + '</div>' +
    '<small class="text-muted">' + (item.email || '') + '</small>' +
    '</div>' +
    '</div>'
  );
  
  return $container;
}

function formatSelection(item) {
  return item.text;
}
```

#### Controller Endpoint
```php
public function searchItems(Request $request)
{
    $query = $request->get('q');
    $page = $request->get('page', 1);
    $perPage = $request->get('per_page', 20);

    $items = YourModel::where('name', 'like', "%{$query}%")
        ->orWhere('email', 'like', "%{$query}%")
        ->paginate($perPage, ['*'], 'page', $page);

    return response()->json([
        'items' => $items->items(),
        'pagination' => [
            'has_more' => $items->hasMorePages()
        ]
    ]);
}
```

### 3. Advanced Select2 Features

#### Tagging (Allow new options)
```javascript
$('.select2-tags').select2({
  tags: true,
  tokenSeparators: [',', ' '],
  createTag: function (params) {
    var term = $.trim(params.term);
    
    if (term === '') {
      return null;
    }
    
    return {
      id: term,
      text: term,
      newTag: true
    };
  }
});
```

#### Grouping Options
```html
<select class="form-select select2" id="groupedSelect">
  <optgroup label="Group 1">
    <option value="1">Option 1</option>
    <option value="2">Option 2</option>
  </optgroup>
  <optgroup label="Group 2">
    <option value="3">Option 3</option>
    <option value="4">Option 4</option>
  </optgroup>
</select>
```

---

## ✅ Form Validation

### 1. Basic Setup

#### Assets Required
```php
@section('vendor-style')
@vite(['resources/assets/vendor/libs/@form-validation/form-validation.scss'])
@endsection

@section('vendor-script')
@vite([
  'resources/assets/vendor/libs/@form-validation/popular.js',
  'resources/assets/vendor/libs/@form-validation/bootstrap5.js',
  'resources/assets/vendor/libs/@form-validation/auto-focus.js'
])
@endsection
```

#### HTML Form
```html
<form id="formValidation" class="row g-3" novalidate>
  <div class="col-md-6">
    <div class="form-floating form-floating-outline">
      <input type="text" class="form-control" id="firstName" name="first_name" placeholder="John">
      <label for="firstName">First Name</label>
    </div>
  </div>
  
  <div class="col-md-6">
    <div class="form-floating form-floating-outline">
      <input type="text" class="form-control" id="lastName" name="last_name" placeholder="Doe">
      <label for="lastName">Last Name</label>
    </div>
  </div>
  
  <div class="col-md-12">
    <div class="form-floating form-floating-outline">
      <input type="email" class="form-control" id="email" name="email" placeholder="<EMAIL>">
      <label for="email">Email</label>
    </div>
  </div>
  
  <div class="col-md-6">
    <div class="form-floating form-floating-outline">
      <select class="form-select select2" id="country" name="country">
        <option value="">Choose country...</option>
        <option value="VN">Vietnam</option>
        <option value="US">United States</option>
      </select>
      <label for="country">Country</label>
    </div>
  </div>
  
  <div class="col-12">
    <button type="submit" class="btn btn-primary">Submit</button>
  </div>
</form>
```

#### JavaScript Validation
```javascript
$(function () {
  const form = document.getElementById('formValidation');
  
  if (form) {
    const fv = FormValidation.formValidation(form, {
      fields: {
        first_name: {
          validators: {
            notEmpty: {
              message: 'First name is required'
            },
            stringLength: {
              min: 2,
              max: 50,
              message: 'First name must be between 2 and 50 characters'
            },
            regexp: {
              regexp: /^[a-zA-Z\s]+$/,
              message: 'First name can only contain letters and spaces'
            }
          }
        },
        last_name: {
          validators: {
            notEmpty: {
              message: 'Last name is required'
            },
            stringLength: {
              min: 2,
              max: 50,
              message: 'Last name must be between 2 and 50 characters'
            }
          }
        },
        email: {
          validators: {
            notEmpty: {
              message: 'Email is required'
            },
            emailAddress: {
              message: 'Please enter a valid email address'
            },
            remote: {
              message: 'This email is already taken',
              method: 'POST',
              url: baseUrl + 'api/check-email',
              data: function() {
                return {
                  email: form.querySelector('[name="email"]').value,
                  _token: $('meta[name="csrf-token"]').attr('content')
                };
              }
            }
          }
        },
        country: {
          validators: {
            notEmpty: {
              message: 'Please select a country'
            }
          }
        }
      },
      plugins: {
        trigger: new FormValidation.plugins.Trigger(),
        bootstrap5: new FormValidation.plugins.Bootstrap5({
          eleValidClass: '',
          rowSelector: '.form-floating, .input-group, .col-md-6, .col-md-12'
        }),
        submitButton: new FormValidation.plugins.SubmitButton(),
        autoFocus: new FormValidation.plugins.AutoFocus()
      }
    }).on('core.form.valid', function() {
      // Form is valid, submit via AJAX
      submitForm();
    });
  }

  function submitForm() {
    const formData = new FormData(form);
    
    $.ajax({
      url: form.action || window.location.href,
      method: 'POST',
      data: formData,
      processData: false,
      contentType: false,
      headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
      },
      success: function(response) {
        Swal.fire({
          title: 'Success!',
          text: 'Form submitted successfully',
          icon: 'success',
          customClass: {
            confirmButton: 'btn btn-primary'
          }
        }).then(() => {
          if (response.redirect) {
            window.location.href = response.redirect;
          }
        });
      },
      error: function(xhr) {
        if (xhr.status === 422) {
          // Validation errors
          const errors = xhr.responseJSON.errors;
          Object.keys(errors).forEach(field => {
            fv.updateFieldStatus(field, 'Invalid', 'remote');
            fv.updateMessage(field, 'remote', errors[field][0]);
          });
        } else {
          Swal.fire({
            title: 'Error!',
            text: 'Something went wrong. Please try again.',
            icon: 'error',
            customClass: {
              confirmButton: 'btn btn-danger'
            }
          });
        }
      }
    });
  }
});
```

### 2. Advanced Validation Rules

#### Custom Validators
```javascript
// Password strength validator
FormValidation.validators.passwordStrength = function() {
  return {
    validate: function(input) {
      const value = input.value;
      
      if (value === '') {
        return { valid: true };
      }
      
      const hasLower = /[a-z]/.test(value);
      const hasUpper = /[A-Z]/.test(value);
      const hasNumber = /[0-9]/.test(value);
      const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(value);
      const isLongEnough = value.length >= 8;
      
      const score = [hasLower, hasUpper, hasNumber, hasSpecial, isLongEnough]
        .reduce((acc, curr) => acc + (curr ? 1 : 0), 0);
      
      return {
        valid: score >= 4,
        message: 'Password must contain at least 4 of: lowercase, uppercase, number, special character, 8+ characters'
      };
    }
  };
};

// Phone number validator
FormValidation.validators.phoneVN = function() {
  return {
    validate: function(input) {
      const value = input.value;
      
      if (value === '') {
        return { valid: true };
      }
      
      // Vietnamese phone number pattern
      const pattern = /^(0|\+84)[3|5|7|8|9][0-9]{8}$/;
      
      return {
        valid: pattern.test(value),
        message: 'Please enter a valid Vietnamese phone number'
      };
    }
  };
};
```

#### Conditional Validation
```javascript
fields: {
  payment_method: {
    validators: {
      notEmpty: {
        message: 'Please select a payment method'
      }
    }
  },
  credit_card: {
    validators: {
      notEmpty: {
        enabled: false,
        message: 'Credit card number is required'
      },
      creditCard: {
        enabled: false,
        message: 'Please enter a valid credit card number'
      }
    }
  }
}

// Enable/disable validation based on payment method
fv.on('core.field.changed', function(e) {
  if (e.field === 'payment_method') {
    const isCredit = e.element.value === 'credit_card';
    
    fv.enableValidator('credit_card', 'notEmpty', isCredit)
      .enableValidator('credit_card', 'creditCard', isCredit);
    
    if (!isCredit) {
      fv.resetField('credit_card');
    }
  }
});
```

---

## 🎨 Other Form Components

### 1. Date Pickers

#### Flatpickr Setup
```php
@section('vendor-style')
@vite(['resources/assets/vendor/libs/flatpickr/flatpickr.scss'])
@endsection

@section('vendor-script')
@vite(['resources/assets/vendor/libs/flatpickr/flatpickr.js'])
@endsection
```

```html
<!-- Date Picker -->
<div class="form-floating form-floating-outline">
  <input type="text" class="form-control flatpickr-date" id="dateInput" name="date" placeholder="Select date">
  <label for="dateInput">Date</label>
</div>

<!-- Date Range -->
<div class="form-floating form-floating-outline">
  <input type="text" class="form-control flatpickr-range" id="dateRange" name="date_range" placeholder="Select date range">
  <label for="dateRange">Date Range</label>
</div>

<!-- Date Time -->
<div class="form-floating form-floating-outline">
  <input type="text" class="form-control flatpickr-datetime" id="datetime" name="datetime" placeholder="Select date and time">
  <label for="datetime">Date & Time</label>
</div>
```

```javascript
// Date picker
$('.flatpickr-date').flatpickr({
  dateFormat: 'd/m/Y',
  locale: 'vn'
});

// Date range
$('.flatpickr-range').flatpickr({
  mode: 'range',
  dateFormat: 'd/m/Y'
});

// Date time
$('.flatpickr-datetime').flatpickr({
  enableTime: true,
  dateFormat: 'd/m/Y H:i',
  time_24hr: true
});
```

### 2. File Upload

#### Dropzone Setup
```php
@section('vendor-style')
@vite(['resources/assets/vendor/libs/dropzone/dropzone.scss'])
@endsection

@section('vendor-script')
@vite(['resources/assets/vendor/libs/dropzone/dropzone.js'])
@endsection
```

```html
<div class="card">
  <div class="card-body">
    <form action="{{ route('upload') }}" class="dropzone" id="fileDropzone">
      @csrf
    </form>
  </div>
</div>
```

```javascript
Dropzone.autoDiscover = false;

const fileDropzone = new Dropzone('#fileDropzone', {
  url: baseUrl + 'upload',
  maxFilesize: 5, // MB
  acceptedFiles: '.jpg,.jpeg,.png,.pdf,.doc,.docx',
  addRemoveLinks: true,
  headers: {
    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
  },
  success: function(file, response) {
    console.log('File uploaded successfully:', response);
  },
  error: function(file, response) {
    console.log('Upload error:', response);
  }
});
```

### 3. Rich Text Editor

#### Quill Setup
```php
@section('vendor-style')
@vite(['resources/assets/vendor/libs/quill/editor.scss'])
@endsection

@section('vendor-script')
@vite(['resources/assets/vendor/libs/quill/quill.js'])
@endsection
```

```html
<div class="form-floating form-floating-outline">
  <div id="editor" style="height: 200px;"></div>
  <input type="hidden" name="content" id="hiddenContent">
  <label>Content</label>
</div>
```

```javascript
const quill = new Quill('#editor', {
  theme: 'snow',
  modules: {
    toolbar: [
      [{ 'header': [1, 2, 3, false] }],
      ['bold', 'italic', 'underline'],
      ['link', 'blockquote', 'code-block'],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      ['clean']
    ]
  }
});

// Update hidden input on content change
quill.on('text-change', function() {
  document.getElementById('hiddenContent').value = quill.root.innerHTML;
});
```

---

## 🔔 SweetAlert2 Integration

### 1. Basic Alerts

```javascript
// Success alert
Swal.fire({
  title: 'Success!',
  text: 'Operation completed successfully',
  icon: 'success',
  confirmButtonText: 'OK',
  customClass: {
    confirmButton: 'btn btn-primary'
  }
});

// Error alert
Swal.fire({
  title: 'Error!',
  text: 'Something went wrong',
  icon: 'error',
  confirmButtonText: 'OK',
  customClass: {
    confirmButton: 'btn btn-danger'
  }
});

// Warning with confirmation
Swal.fire({
  title: 'Are you sure?',
  text: 'This action cannot be undone',
  icon: 'warning',
  showCancelButton: true,
  confirmButtonText: 'Yes, delete it!',
  cancelButtonText: 'Cancel',
  customClass: {
    confirmButton: 'btn btn-danger me-2',
    cancelButton: 'btn btn-outline-secondary'
  }
}).then((result) => {
  if (result.isConfirmed) {
    // Perform action
    performDelete();
  }
});
```

### 2. Advanced SweetAlert2

#### Input Dialog
```javascript
Swal.fire({
  title: 'Enter your name',
  input: 'text',
  inputPlaceholder: 'Your name...',
  showCancelButton: true,
  inputValidator: (value) => {
    if (!value) {
      return 'You need to write something!'
    }
  }
}).then((result) => {
  if (result.isConfirmed) {
    console.log('Name:', result.value);
  }
});
```

#### Progress Dialog
```javascript
let timerInterval;
Swal.fire({
  title: 'Processing...',
  html: 'Please wait <b></b> seconds.',
  timer: 5000,
  timerProgressBar: true,
  allowOutsideClick: false,
  didOpen: () => {
    Swal.showLoading();
    const b = Swal.getHtmlContainer().querySelector('b');
    timerInterval = setInterval(() => {
      b.textContent = Math.ceil(Swal.getTimerLeft() / 1000);
    }, 100);
  },
  willClose: () => {
    clearInterval(timerInterval);
  }
});
```

---

## 🎯 Best Practices

### 1. Form Structure
- Sử dụng `form-floating` cho consistent styling
- Group related fields trong `row` và `col-*`
- Luôn include proper labels và placeholders
- Sử dụng semantic HTML5 input types

### 2. Validation
- Validate cả client-side và server-side
- Provide clear, helpful error messages
- Use real-time validation cho better UX
- Handle AJAX errors gracefully

### 3. Accessibility
- Proper label associations
- ARIA attributes khi cần thiết
- Keyboard navigation support
- Screen reader compatibility

### 4. Performance
- Load only required assets
- Use CDN cho external libraries
- Minimize DOM manipulations
- Cache AJAX responses khi có thể

---

*Hướng dẫn này cung cấp foundation hoàn chỉnh cho việc implement form components trong dự án VPCC với Bootstrap 5 và modern JavaScript libraries.*
