# CRUD Module Template - Mẫu Tạo Module CRUD

## 📋 Checklist Tạo Module CRUD

### 1. <PERSON><PERSON><PERSON> Bị
- [ ] Xác định tên module (VD: `ProductCategory`)
- [ ] Thiết kế database schema
- [ ] Tạo migration file
- [ ] Xác định relationships với models khác

### 2. Backend Implementation
- [ ] Tạo Model với relationships và scopes
- [ ] Tạo Controller với CRUD methods
- [ ] Tạo Form Request classes cho validation
- [ ] Thêm routes vào `web.php`
- [ ] Tạo Seeder (nếu cần)

### 3. Frontend Implementation
- [ ] Tạo Blade views (index, create, edit, show)
- [ ] Tạo custom JavaScript file
- [ ] Tạo custom CSS file (nếu cần)
- [ ] Cập nhật menu configuration
- [ ] Test responsive design

### 4. Testing
- [ ] Test CRUD operations
- [ ] Test DataTables functionality
- [ ] Test form validation
- [ ] Test responsive design
- [ ] Test permissions (nếu có)

---

## 🗂️ File Structure

```
app/
├── Http/Controllers/Admin/
│   └── YourModelController.php
├── Models/
│   └── YourModel.php
├── Http/Requests/
│   ├── StoreYourModelRequest.php
│   └── UpdateYourModelRequest.php
└── Policies/
    └── YourModelPolicy.php (optional)

resources/
├── views/admin/your-models/
│   ├── index.blade.php
│   ├── create.blade.php
│   ├── edit.blade.php
│   ├── show.blade.php
│   └── partials/
│       ├── form.blade.php
│       └── actions.blade.php
├── js/
│   └── your-model-management.js
└── css/
    └── your-model.css (optional)

database/
├── migrations/
│   └── xxxx_xx_xx_create_your_models_table.php
└── seeders/
    └── YourModelSeeder.php (optional)
```

---

## 📝 Code Templates

### 1. Migration Template
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('your_models', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->integer('sort_order')->default(0);
            $table->foreignId('category_id')->nullable()->constrained()->onDelete('set null');
            $table->timestamps();
            
            $table->index(['status', 'sort_order']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('your_models');
    }
};
```

### 2. Model Template
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class YourModel extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'status',
        'sort_order',
        'category_id',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    // Relationships
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeInactive($query)
    {
        return $query->where('status', 'inactive');
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    // Accessors
    public function getStatusBadgeAttribute()
    {
        $class = $this->status === 'active' ? 'success' : 'secondary';
        return '<span class="badge bg-label-' . $class . '">' . ucfirst($this->status) . '</span>';
    }

    public function getFormattedCreatedAtAttribute()
    {
        return $this->created_at->format('d/m/Y H:i');
    }
}
```

### 3. Controller Template
```php
<?php

namespace App\Http\Controllers\Admin;

use App\Models\YourModel;
use App\Http\Requests\StoreYourModelRequest;
use App\Http\Requests\UpdateYourModelRequest;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;

class YourModelController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        // $this->middleware('permission:manage-your-models'); // If using permissions
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $totalCount = YourModel::count();
        $activeCount = YourModel::active()->count();
        $inactiveCount = YourModel::inactive()->count();

        return view('admin.your-models.index', compact(
            'totalCount',
            'activeCount', 
            'inactiveCount'
        ));
    }

    /**
     * Get data for DataTables
     */
    public function getData(Request $request)
    {
        $query = YourModel::with('category');

        // Handle custom filters
        if ($request->has('status_filter') && !empty($request->status_filter)) {
            $query->where('status', $request->status_filter);
        }

        if ($request->has('category_filter') && !empty($request->category_filter)) {
            $query->where('category_id', $request->category_filter);
        }

        // Handle search
        if ($request->has('search') && !empty($request->search['value'])) {
            $searchValue = $request->search['value'];
            $query->where(function ($q) use ($searchValue) {
                $q->where('name', 'like', "%{$searchValue}%")
                  ->orWhere('description', 'like', "%{$searchValue}%");
            });
        }

        // Handle ordering
        if ($request->has('order')) {
            $orderColumn = $request->columns[$request->order[0]['column']]['data'];
            $orderDirection = $request->order[0]['dir'];
            
            if ($orderColumn === 'category_name') {
                $query->join('categories', 'your_models.category_id', '=', 'categories.id')
                      ->orderBy('categories.name', $orderDirection)
                      ->select('your_models.*');
            } else {
                $query->orderBy($orderColumn, $orderDirection);
            }
        } else {
            $query->ordered();
        }

        // Pagination
        $recordsTotal = YourModel::count();
        $recordsFiltered = $query->count();
        
        $start = $request->start ?? 0;
        $length = $request->length ?? 10;
        
        $data = $query->skip($start)->take($length)->get();

        // Transform data for DataTables
        $data = $data->map(function ($item) {
            return [
                'id' => $item->id,
                'name' => $item->name,
                'description' => $item->description ? \Str::limit($item->description, 50) : '-',
                'category_name' => $item->category ? $item->category->name : '-',
                'status_badge' => $item->status_badge,
                'sort_order' => $item->sort_order,
                'created_at' => $item->formatted_created_at,
                'actions' => view('admin.your-models.partials.actions', compact('item'))->render()
            ];
        });

        return response()->json([
            'draw' => $request->draw,
            'recordsTotal' => $recordsTotal,
            'recordsFiltered' => $recordsFiltered,
            'data' => $data
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = Category::active()->ordered()->get();
        return view('admin.your-models.create', compact('categories'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreYourModelRequest $request)
    {
        $data = $request->validated();
        
        // Set sort order if not provided
        if (!isset($data['sort_order'])) {
            $data['sort_order'] = YourModel::max('sort_order') + 1;
        }

        $yourModel = YourModel::create($data);

        return redirect()
            ->route('admin.your-models.index')
            ->with('success', 'Item created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(YourModel $yourModel)
    {
        $yourModel->load('category');
        return view('admin.your-models.show', compact('yourModel'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(YourModel $yourModel)
    {
        $categories = Category::active()->ordered()->get();
        return view('admin.your-models.edit', compact('yourModel', 'categories'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateYourModelRequest $request, YourModel $yourModel)
    {
        $data = $request->validated();
        $yourModel->update($data);

        return redirect()
            ->route('admin.your-models.index')
            ->with('success', 'Item updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(YourModel $yourModel)
    {
        try {
            $yourModel->delete();
            
            return response()->json([
                'success' => true,
                'message' => 'Item deleted successfully.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error deleting item: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update sort order
     */
    public function updateSortOrder(Request $request)
    {
        $request->validate([
            'items' => 'required|array',
            'items.*.id' => 'required|exists:your_models,id',
            'items.*.sort_order' => 'required|integer|min:0'
        ]);

        foreach ($request->items as $item) {
            YourModel::where('id', $item['id'])
                ->update(['sort_order' => $item['sort_order']]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Sort order updated successfully.'
        ]);
    }
}
```

### 4. Form Request Templates

#### StoreYourModelRequest.php
```php
<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreYourModelRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Or check permissions
    }

    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255|unique:your_models,name',
            'description' => 'nullable|string|max:1000',
            'status' => 'required|in:active,inactive',
            'sort_order' => 'nullable|integer|min:0',
            'category_id' => 'nullable|exists:categories,id',
        ];
    }

    public function messages(): array
    {
        return [
            'name.required' => 'Name is required.',
            'name.unique' => 'This name already exists.',
            'status.required' => 'Status is required.',
            'status.in' => 'Status must be active or inactive.',
            'category_id.exists' => 'Selected category does not exist.',
        ];
    }
}
```

#### UpdateYourModelRequest.php
```php
<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateYourModelRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Or check permissions
    }

    public function rules(): array
    {
        return [
            'name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('your_models', 'name')->ignore($this->your_model->id)
            ],
            'description' => 'nullable|string|max:1000',
            'status' => 'required|in:active,inactive',
            'sort_order' => 'nullable|integer|min:0',
            'category_id' => 'nullable|exists:categories,id',
        ];
    }

    public function messages(): array
    {
        return [
            'name.required' => 'Name is required.',
            'name.unique' => 'This name already exists.',
            'status.required' => 'Status is required.',
            'status.in' => 'Status must be active or inactive.',
            'category_id.exists' => 'Selected category does not exist.',
        ];
    }
}
```

### 5. Routes Template
```php
// routes/web.php
Route::prefix('admin')->name('admin.')->middleware(['auth'])->group(function () {
    // Your Model Management
    Route::resource('your-models', YourModelController::class);
    Route::get('your-models-data', [YourModelController::class, 'getData'])->name('your-models.data');
    Route::post('your-models/sort-order', [YourModelController::class, 'updateSortOrder'])->name('your-models.sort-order');
});
```

---

## 🎨 View Templates

### 1. Index View Template
```php
@extends('layouts/layoutMaster')

@section('title', 'Your Model Management')

@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-buttons-bs5/buttons.bootstrap5.scss',
  'resources/assets/vendor/libs/select2/select2.scss',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss'
])
@endsection

@section('vendor-script')
@vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js',
  'resources/assets/vendor/libs/select2/select2.js',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js'
])
@endsection

@section('page-script')
@vite([
  'resources/js/datatables-config.js',
  'resources/js/your-model-management.js'
])
@endsection

@section('content')
<!-- Statistics Cards -->
<div class="row g-6 mb-6">
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div class="me-1">
            <p class="text-heading mb-1">Total Items</p>
            <div class="d-flex align-items-center">
              <h4 class="mb-1 me-2">{{ $totalCount }}</h4>
            </div>
            <small class="mb-0">All items</small>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-primary rounded-3">
              <div class="ri-file-list-line ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div class="me-1">
            <p class="text-heading mb-1">Active</p>
            <div class="d-flex align-items-center">
              <h4 class="mb-1 me-2">{{ $activeCount }}</h4>
            </div>
            <small class="mb-0">Active items</small>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-success rounded-3">
              <div class="ri-check-line ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-sm-6 col-xl-3">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div class="me-1">
            <p class="text-heading mb-1">Inactive</p>
            <div class="d-flex align-items-center">
              <h4 class="mb-1 me-2">{{ $inactiveCount }}</h4>
            </div>
            <small class="mb-0">Inactive items</small>
          </div>
          <div class="avatar">
            <div class="avatar-initial bg-label-secondary rounded-3">
              <div class="ri-close-line ri-26px"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Filters -->
<div class="card mb-6">
  <div class="card-body">
    <div class="row g-3">
      <div class="col-md-3">
        <div class="form-floating form-floating-outline">
          <select id="StatusFilter" class="form-select">
            <option value="">All Status</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
          </select>
          <label for="StatusFilter">Status</label>
        </div>
      </div>
      <div class="col-md-3">
        <div class="form-floating form-floating-outline">
          <select id="CategoryFilter" class="form-select select2">
            <option value="">All Categories</option>
            @foreach($categories ?? [] as $category)
              <option value="{{ $category->id }}">{{ $category->name }}</option>
            @endforeach
          </select>
          <label for="CategoryFilter">Category</label>
        </div>
      </div>
      <div class="col-md-3">
        <button type="button" class="btn btn-outline-secondary" id="clearFilters">
          <i class="ri-refresh-line me-1"></i>Clear Filters
        </button>
      </div>
    </div>
  </div>
</div>

<!-- DataTable -->
<div class="card">
  <div class="card-header d-flex justify-content-between align-items-center">
    <h5 class="card-title mb-0">Your Models List</h5>
    <a href="{{ route('admin.your-models.create') }}" class="btn btn-primary">
      <i class="ri-add-line me-1"></i>Add New
    </a>
  </div>
  <div class="card-datatable table-responsive">
    <table class="datatables-your-models table table-bordered">
      <thead>
        <tr>
          <th></th>
          <th>ID</th>
          <th>Name</th>
          <th>Description</th>
          <th>Category</th>
          <th>Status</th>
          <th>Order</th>
          <th>Created</th>
          <th>Actions</th>
        </tr>
      </thead>
    </table>
  </div>
</div>
@endsection
```

### 2. Form Partial Template
```php
{{-- resources/views/admin/your-models/partials/form.blade.php --}}
<div class="row">
  <div class="col-md-8">
    <div class="card mb-6">
      <div class="card-header">
        <h5 class="card-title mb-0">Basic Information</h5>
      </div>
      <div class="card-body">
        <div class="row g-3">
          <div class="col-md-12">
            <div class="form-floating form-floating-outline">
              <input type="text" 
                     class="form-control @error('name') is-invalid @enderror" 
                     id="name" 
                     name="name" 
                     value="{{ old('name', $yourModel->name ?? '') }}" 
                     placeholder="Enter name">
              <label for="name">Name *</label>
              @error('name')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
          </div>
          
          <div class="col-md-12">
            <div class="form-floating form-floating-outline">
              <textarea class="form-control @error('description') is-invalid @enderror" 
                        id="description" 
                        name="description" 
                        rows="4" 
                        placeholder="Enter description">{{ old('description', $yourModel->description ?? '') }}</textarea>
              <label for="description">Description</label>
              @error('description')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <div class="col-md-4">
    <div class="card mb-6">
      <div class="card-header">
        <h5 class="card-title mb-0">Settings</h5>
      </div>
      <div class="card-body">
        <div class="row g-3">
          <div class="col-12">
            <div class="form-floating form-floating-outline">
              <select class="form-select select2 @error('category_id') is-invalid @enderror" 
                      id="category_id" 
                      name="category_id">
                <option value="">Choose category...</option>
                @foreach($categories as $category)
                  <option value="{{ $category->id }}" 
                          {{ old('category_id', $yourModel->category_id ?? '') == $category->id ? 'selected' : '' }}>
                    {{ $category->name }}
                  </option>
                @endforeach
              </select>
              <label for="category_id">Category</label>
              @error('category_id')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
          </div>
          
          <div class="col-12">
            <div class="form-floating form-floating-outline">
              <select class="form-select @error('status') is-invalid @enderror" 
                      id="status" 
                      name="status">
                <option value="active" {{ old('status', $yourModel->status ?? 'active') === 'active' ? 'selected' : '' }}>
                  Active
                </option>
                <option value="inactive" {{ old('status', $yourModel->status ?? '') === 'inactive' ? 'selected' : '' }}>
                  Inactive
                </option>
              </select>
              <label for="status">Status *</label>
              @error('status')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
          </div>
          
          <div class="col-12">
            <div class="form-floating form-floating-outline">
              <input type="number" 
                     class="form-control @error('sort_order') is-invalid @enderror" 
                     id="sort_order" 
                     name="sort_order" 
                     value="{{ old('sort_order', $yourModel->sort_order ?? 0) }}" 
                     min="0" 
                     placeholder="0">
              <label for="sort_order">Sort Order</label>
              @error('sort_order')
                <div class="invalid-feedback">{{ $message }}</div>
              @enderror
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="card">
      <div class="card-body">
        <div class="d-grid gap-2">
          <button type="submit" class="btn btn-primary">
            <i class="ri-save-line me-1"></i>
            {{ isset($yourModel) ? 'Update' : 'Create' }}
          </button>
          <a href="{{ route('admin.your-models.index') }}" class="btn btn-outline-secondary">
            <i class="ri-arrow-left-line me-1"></i>Back to List
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
```

### 3. Actions Partial Template
```php
{{-- resources/views/admin/your-models/partials/actions.blade.php --}}
<div class="d-flex align-items-center gap-2">
  <a href="{{ route('admin.your-models.show', $item->id) }}" 
     class="btn btn-sm btn-outline-info" 
     title="View">
    <i class="ri-eye-line"></i>
  </a>
  
  <a href="{{ route('admin.your-models.edit', $item->id) }}" 
     class="btn btn-sm btn-outline-primary" 
     title="Edit">
    <i class="ri-edit-line"></i>
  </a>
  
  <button type="button" 
          class="btn btn-sm btn-outline-danger delete-record" 
          data-id="{{ $item->id }}" 
          data-name="{{ $item->name }}" 
          title="Delete">
    <i class="ri-delete-bin-line"></i>
  </button>
</div>
```

---

## 📱 JavaScript Template

```javascript
/**
 * Your Model Management JavaScript
 */

'use strict';

$(function () {
  let borderColor, bodyBg, headingColor;

  if (isDarkStyle) {
    borderColor = config.colors_dark.borderColor;
    bodyBg = config.colors_dark.bodyBg;
    headingColor = config.colors_dark.headingColor;
  } else {
    borderColor = config.colors.borderColor;
    bodyBg = config.colors.bodyBg;
    headingColor = config.colors.headingColor;
  }

  // Variable declaration for table
  var dt_table = $('.datatables-your-models');

  // DataTable initialization
  if (dt_table.length) {
    var defaultConfig = DataTablesConfig.getDefaultConfig(isDarkStyle, config);

    var dt = dt_table.DataTable($.extend(true, {}, defaultConfig, {
      ajax: {
        url: baseUrl + 'admin/your-models-data',
        type: 'GET',
        data: function(d) {
          d.status_filter = $('#StatusFilter').val();
          d.category_filter = $('#CategoryFilter').val();
        }
      },
      columns: [
        { data: '' },
        { data: 'id' },
        { data: 'name' },
        { data: 'description' },
        { data: 'category_name' },
        { data: 'status_badge' },
        { data: 'sort_order' },
        { data: 'created_at' },
        { data: 'actions' }
      ],
      columnDefs: DataTablesConfig.getCommonColumnDefs().concat([
        {
          targets: 2,
          responsivePriority: 1,
          render: function (data, type, full, meta) {
            return '<span class="fw-medium">' + data + '</span>';
          }
        },
        {
          targets: 3,
          render: function (data, type, full, meta) {
            return data || '-';
          }
        },
        {
          targets: 5,
          render: function (data, type, full, meta) {
            return data;
          }
        },
        {
          targets: 6,
          className: 'text-center',
          render: function (data, type, full, meta) {
            return '<span class="badge bg-label-info">' + data + '</span>';
          }
        },
        {
          targets: -1,
          title: 'Actions',
          searchable: false,
          orderable: false,
          render: function (data, type, full, meta) {
            return data;
          }
        }
      ])
    }));

    DataTablesConfig.initializeFormControls();
  }

  // Initialize Select2
  $('.select2').select2({
    placeholder: 'Choose option...',
    allowClear: true
  });

  // Apply focus handling for floating labels
  select2Focus('.select2');

  // Filter functionality
  $('#StatusFilter, #CategoryFilter').on('change', function () {
    dt.ajax.reload();
  });

  $('#clearFilters').on('click', function () {
    $('#StatusFilter, #CategoryFilter').val('').trigger('change');
  });

  // Delete functionality
  $(document).on('click', '.delete-record', function () {
    var id = $(this).data('id');
    var name = $(this).data('name');

    Swal.fire({
      title: 'Are you sure?',
      text: 'You want to delete "' + name + '"?',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, delete it!',
      cancelButtonText: 'Cancel',
      customClass: {
        confirmButton: 'btn btn-danger me-2',
        cancelButton: 'btn btn-outline-secondary'
      }
    }).then((result) => {
      if (result.isConfirmed) {
        $.ajax({
          url: baseUrl + 'admin/your-models/' + id,
          type: 'DELETE',
          headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
          },
          success: function(response) {
            dt.ajax.reload();
            Swal.fire({
              title: 'Deleted!',
              text: 'Record has been deleted.',
              icon: 'success',
              customClass: {
                confirmButton: 'btn btn-success'
              }
            });
          },
          error: function(xhr) {
            Swal.fire({
              title: 'Error!',
              text: 'Something went wrong.',
              icon: 'error',
              customClass: {
                confirmButton: 'btn btn-danger'
              }
            });
          }
        });
      }
    });
  });

  // Form validation (if on create/edit pages)
  if ($('#yourModelForm').length) {
    const form = document.getElementById('yourModelForm');
    const fv = FormValidation.formValidation(form, {
      fields: {
        name: {
          validators: {
            notEmpty: {
              message: 'Name is required'
            },
            stringLength: {
              min: 2,
              max: 255,
              message: 'Name must be between 2 and 255 characters'
            }
          }
        },
        status: {
          validators: {
            notEmpty: {
              message: 'Status is required'
            }
          }
        }
      },
      plugins: {
        trigger: new FormValidation.plugins.Trigger(),
        bootstrap5: new FormValidation.plugins.Bootstrap5({
          eleValidClass: '',
          rowSelector: '.form-floating, .input-group'
        }),
        submitButton: new FormValidation.plugins.SubmitButton(),
        autoFocus: new FormValidation.plugins.AutoFocus()
      }
    });
  }
});
```

---

## 🎯 Usage Instructions

1. **Copy templates**: Sao chép các template trên và thay thế `YourModel` bằng tên model thực tế
2. **Update namespaces**: Cập nhật namespace và class names
3. **Customize fields**: Thay đổi fields theo requirements cụ thể
4. **Add to Vite**: Thêm JS/CSS files vào `vite.config.js`
5. **Update menu**: Thêm menu items vào `resources/menu/verticalMenu.json`
6. **Test thoroughly**: Test tất cả functionality trước khi deploy

---

*Template này cung cấp foundation hoàn chỉnh cho việc tạo CRUD modules theo standards của dự án VPCC.*
