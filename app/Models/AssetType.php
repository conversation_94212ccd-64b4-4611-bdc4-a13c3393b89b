<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Storage;

class AssetType extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'icon',
        'image',
        'status',
        'sort_order',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Relationships
     */
    
    /**
     * Get assets that belong to this asset type
     * (Assuming there will be an Asset model in the future)
     */
    public function assets(): HasMany
    {
        return $this->hasMany(Asset::class);
    }

    /**
     * Scopes
     */
    
    /**
     * Scope a query to only include active asset types.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include inactive asset types.
     */
    public function scopeInactive($query)
    {
        return $query->where('status', 'inactive');
    }

    /**
     * Scope a query to order by sort_order and name.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Accessors
     */
    
    /**
     * Get the status badge HTML.
     */
    public function getStatusBadgeAttribute(): string
    {
        $statusConfig = [
            'active' => ['class' => 'success', 'text' => 'Active'],
            'inactive' => ['class' => 'secondary', 'text' => 'Inactive'],
        ];
        
        $config = $statusConfig[$this->status] ?? $statusConfig['inactive'];
        return '<span class="badge bg-label-' . $config['class'] . '">' . $config['text'] . '</span>';
    }

    /**
     * Get the formatted created at date.
     */
    public function getFormattedCreatedAtAttribute(): string
    {
        return $this->created_at->format('d/m/Y H:i');
    }

    /**
     * Get the formatted updated at date.
     */
    public function getFormattedUpdatedAtAttribute(): string
    {
        return $this->updated_at->format('d/m/Y H:i');
    }

    /**
     * Get the icon HTML.
     */
    public function getIconHtmlAttribute(): string
    {
        if (!$this->icon) {
            return '<i class="ri-file-line ri-22px text-muted"></i>';
        }

        // If icon starts with 'ri-', it's a RemixIcon class
        if (str_starts_with($this->icon, 'ri-')) {
            return '<i class="' . $this->icon . ' ri-22px text-primary"></i>';
        }

        // Otherwise, treat as image path
        $imagePath = Storage::url($this->icon);
        return '<img src="' . $imagePath . '" alt="' . $this->name . '" class="rounded" style="width: 22px; height: 22px;">';
    }

    /**
     * Get the image URL.
     */
    public function getImageUrlAttribute(): ?string
    {
        if (!$this->image) {
            return null;
        }

        return Storage::url($this->image);
    }

    /**
     * Get the display name with icon.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->icon_html . ' <span class="ms-2">' . $this->name . '</span>';
    }

    /**
     * Mutators
     */
    
    /**
     * Set the name attribute.
     */
    public function setNameAttribute($value): void
    {
        $this->attributes['name'] = trim($value);
    }

    /**
     * Set the description attribute.
     */
    public function setDescriptionAttribute($value): void
    {
        $this->attributes['description'] = $value ? trim($value) : null;
    }

    /**
     * Static methods
     */
    
    /**
     * Get the next sort order.
     */
    public static function getNextSortOrder(): int
    {
        return static::max('sort_order') + 1;
    }

    /**
     * Get active asset types for dropdown.
     */
    public static function getActiveOptions(): array
    {
        return static::active()
            ->ordered()
            ->pluck('name', 'id')
            ->toArray();
    }

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        // Set default sort order when creating
        static::creating(function ($model) {
            if (!$model->sort_order) {
                $model->sort_order = static::getNextSortOrder();
            }
        });

        // Clean up files when deleting
        static::deleting(function ($model) {
            if ($model->image && Storage::exists($model->image)) {
                Storage::delete($model->image);
            }
            
            if ($model->icon && !str_starts_with($model->icon, 'ri-') && Storage::exists($model->icon)) {
                Storage::delete($model->icon);
            }
        });
    }
}
