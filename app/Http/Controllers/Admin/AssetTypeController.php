<?php

namespace App\Http\Controllers\Admin;

use App\Models\AssetType;
use App\Http\Requests\StoreAssetTypeRequest;
use App\Http\Requests\UpdateAssetTypeRequest;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Storage;

class AssetTypeController extends Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->middleware('auth');
        // $this->middleware('permission:manage-asset-types'); // Uncomment if using permissions
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // Statistics for dashboard cards
        $totalCount = AssetType::count();
        $activeCount = AssetType::active()->count();
        $inactiveCount = AssetType::inactive()->count();

        return view('admin.asset-types.index', compact(
            'totalCount',
            'activeCount', 
            'inactiveCount'
        ));
    }

    /**
     * Get data for DataTables
     */
    public function getData(Request $request)
    {
        $query = AssetType::query();

        // Handle custom filters
        if ($request->has('status_filter') && !empty($request->status_filter)) {
            $query->where('status', $request->status_filter);
        }

        // Handle search
        if ($request->has('search') && !empty($request->search['value'])) {
            $searchValue = $request->search['value'];
            $query->where(function ($q) use ($searchValue) {
                $q->where('name', 'like', "%{$searchValue}%")
                  ->orWhere('description', 'like', "%{$searchValue}%");
            });
        }

        // Handle ordering
        if ($request->has('order')) {
            $orderColumn = $request->columns[$request->order[0]['column']]['data'];
            $orderDirection = $request->order[0]['dir'];
            
            $query->orderBy($orderColumn, $orderDirection);
        } else {
            $query->ordered();
        }

        // Pagination
        $recordsTotal = AssetType::count();
        $recordsFiltered = $query->count();
        
        $start = $request->start ?? 0;
        $length = $request->length ?? 10;
        
        $data = $query->skip($start)->take($length)->get();

        // Transform data for DataTables
        $data = $data->map(function ($item) {
            return [
                'id' => $item->id,
                'name' => $item->name,
                'description' => $item->description ? \Str::limit($item->description, 50) : '-',
                'icon_html' => $item->icon_html,
                'status_badge' => $item->status_badge,
                'sort_order' => $item->sort_order,
                'created_at' => $item->formatted_created_at,
                'actions' => view('admin.asset-types.partials.actions', compact('item'))->render()
            ];
        });

        return response()->json([
            'draw' => $request->draw,
            'recordsTotal' => $recordsTotal,
            'recordsFiltered' => $recordsFiltered,
            'data' => $data
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.asset-types.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreAssetTypeRequest $request)
    {
        $data = $request->validated();
        
        // Handle image upload
        if ($request->hasFile('image')) {
            $data['image'] = $request->file('image')->store('asset-types', 'public');
        }

        // Set sort order if not provided
        if (!isset($data['sort_order'])) {
            $data['sort_order'] = AssetType::getNextSortOrder();
        }

        $assetType = AssetType::create($data);

        return redirect()
            ->route('admin.asset-types.index')
            ->with('success', 'Loại tài sản đã được tạo thành công.');
    }

    /**
     * Display the specified resource.
     */
    public function show(AssetType $assetType)
    {
        return view('admin.asset-types.show', compact('assetType'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(AssetType $assetType)
    {
        return view('admin.asset-types.edit', compact('assetType'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateAssetTypeRequest $request, AssetType $assetType)
    {
        $data = $request->validated();
        
        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($assetType->image && Storage::disk('public')->exists($assetType->image)) {
                Storage::disk('public')->delete($assetType->image);
            }
            
            $data['image'] = $request->file('image')->store('asset-types', 'public');
        }

        // Handle image removal
        if ($request->remove_image && $assetType->image) {
            if (Storage::disk('public')->exists($assetType->image)) {
                Storage::disk('public')->delete($assetType->image);
            }
            $data['image'] = null;
        }

        $assetType->update($data);

        return redirect()
            ->route('admin.asset-types.index')
            ->with('success', 'Loại tài sản đã được cập nhật thành công.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(AssetType $assetType)
    {
        try {
            // Check if asset type is being used (if Asset model exists)
            // if ($assetType->assets()->count() > 0) {
            //     return response()->json([
            //         'success' => false,
            //         'message' => 'Không thể xóa loại tài sản này vì đang được sử dụng.'
            //     ], 422);
            // }

            $assetType->delete();
            
            return response()->json([
                'success' => true,
                'message' => 'Loại tài sản đã được xóa thành công.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi xóa loại tài sản: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update sort order
     */
    public function updateSortOrder(Request $request)
    {
        $request->validate([
            'items' => 'required|array',
            'items.*.id' => 'required|exists:asset_types,id',
            'items.*.sort_order' => 'required|integer|min:0'
        ]);

        try {
            foreach ($request->items as $item) {
                AssetType::where('id', $item['id'])
                    ->update(['sort_order' => $item['sort_order']]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Thứ tự sắp xếp đã được cập nhật thành công.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi cập nhật thứ tự: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Toggle status
     */
    public function toggleStatus(AssetType $assetType)
    {
        try {
            $newStatus = $assetType->status === 'active' ? 'inactive' : 'active';
            $assetType->update(['status' => $newStatus]);

            return response()->json([
                'success' => true,
                'message' => 'Trạng thái đã được cập nhật thành công.',
                'new_status' => $newStatus,
                'status_badge' => $assetType->status_badge
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi cập nhật trạng thái: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get asset types for API/AJAX calls
     */
    public function getAssetTypes(Request $request)
    {
        $query = AssetType::active()->ordered();

        if ($request->has('q') && !empty($request->q)) {
            $query->where('name', 'like', '%' . $request->q . '%');
        }

        $assetTypes = $query->get(['id', 'name', 'icon']);

        return response()->json([
            'items' => $assetTypes->map(function ($item) {
                return [
                    'id' => $item->id,
                    'text' => $item->name,
                    'icon' => $item->icon
                ];
            })
        ]);
    }
}
