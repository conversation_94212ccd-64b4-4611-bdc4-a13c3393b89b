<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateAssetTypeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Or implement permission check: auth()->user()->can('update-asset-types')
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $assetTypeId = $this->route('asset_type')->id ?? $this->route('asset_type');

        return [
            'name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('asset_types', 'name')->ignore($assetTypeId)
            ],
            'description' => 'nullable|string|max:1000',
            'icon' => 'nullable|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'status' => 'required|in:active,inactive',
            'sort_order' => 'nullable|integer|min:0',
            'remove_image' => 'nullable|boolean', // For removing existing image
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Tên loại tài sản là bắt buộc.',
            'name.string' => 'Tên loại tài sản phải là chuỗi ký tự.',
            'name.max' => 'Tên loại tài sản không được vượt quá 255 ký tự.',
            'name.unique' => 'Tên loại tài sản này đã tồn tại.',
            
            'description.string' => 'Mô tả phải là chuỗi ký tự.',
            'description.max' => 'Mô tả không được vượt quá 1000 ký tự.',
            
            'icon.string' => 'Icon phải là chuỗi ký tự.',
            'icon.max' => 'Icon không được vượt quá 255 ký tự.',
            
            'image.image' => 'File phải là hình ảnh.',
            'image.mimes' => 'Hình ảnh phải có định dạng: jpeg, png, jpg, gif, svg.',
            'image.max' => 'Kích thước hình ảnh không được vượt quá 2MB.',
            
            'status.required' => 'Trạng thái là bắt buộc.',
            'status.in' => 'Trạng thái phải là active hoặc inactive.',
            
            'sort_order.integer' => 'Thứ tự sắp xếp phải là số nguyên.',
            'sort_order.min' => 'Thứ tự sắp xếp phải lớn hơn hoặc bằng 0.',
            
            'remove_image.boolean' => 'Tùy chọn xóa hình ảnh không hợp lệ.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'name' => 'tên loại tài sản',
            'description' => 'mô tả',
            'icon' => 'icon',
            'image' => 'hình ảnh',
            'status' => 'trạng thái',
            'sort_order' => 'thứ tự sắp xếp',
            'remove_image' => 'xóa hình ảnh',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Trim whitespace from string fields
        $this->merge([
            'name' => $this->name ? trim($this->name) : null,
            'description' => $this->description ? trim($this->description) : null,
            'icon' => $this->icon ? trim($this->icon) : null,
            'remove_image' => $this->has('remove_image') ? (bool) $this->remove_image : false,
        ]);
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Custom validation logic can be added here
            
            // Validate icon format if provided
            if ($this->icon && !empty($this->icon)) {
                // Check if it's a valid RemixIcon class or file path
                if (!str_starts_with($this->icon, 'ri-') && !filter_var($this->icon, FILTER_VALIDATE_URL)) {
                    // If not a RemixIcon class and not a URL, it should be a valid file path format
                    if (!preg_match('/^[a-zA-Z0-9\/_\-\.]+$/', $this->icon)) {
                        $validator->errors()->add('icon', 'Icon phải là class RemixIcon (ri-*) hoặc đường dẫn file hợp lệ.');
                    }
                }
            }
        });
    }
}
