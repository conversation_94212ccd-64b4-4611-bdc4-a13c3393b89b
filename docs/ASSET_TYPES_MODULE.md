# Asset Types Module Documentation

## Tổng Quan

Module **Asset Types** (Loại Tà<PERSON>) là một CRUD module hoàn chỉnh được phát triển theo standards của dự án VPCC. Module này quản lý các loại tài sản trong hệ thống với đầy đủ chức năng tạo, đọc, cập nhật và xóa.

## Tính Năng

### 1. Quản Lý Loại Tài Sản
- ✅ Tạo mới loại tài sản
- ✅ Xem danh sách với DataTables
- ✅ Chỉnh sửa thông tin
- ✅ Xem chi tiết
- ✅ Xóa loại tài sản
- ✅ Thay đổi trạng thái (active/inactive)
- ✅ Sắp xếp thứ tự

### 2. Tính Năng Nâng Cao
- ✅ Upload và quản lý hình ảnh
- ✅ Icon support (RemixIcon classes)
- ✅ Form validation với FormValidation library
- ✅ Real-time preview
- ✅ Responsive design
- ✅ Dark/Light theme support
- ✅ Export data (CSV, Excel, PDF, Print)
- ✅ Advanced filtering và search

## Cấu Trúc Files

```
app/
├── Http/
│   ├── Controllers/Admin/
│   │   └── AssetTypeController.php
│   └── Requests/
│       ├── StoreAssetTypeRequest.php
│       └── UpdateAssetTypeRequest.php
├── Models/
│   └── AssetType.php
database/
└── migrations/
    └── 2025_01_21_000000_create_asset_types_table.php
resources/
├── views/admin/asset-types/
│   ├── index.blade.php
│   ├── create.blade.php
│   ├── edit.blade.php
│   ├── show.blade.php
│   └── partials/
│       ├── form.blade.php
│       └── actions.blade.php
├── js/
│   ├── asset-type-management.js
│   └── asset-type-form.js
└── css/
    └── asset-types.css
routes/
└── web.php (updated)
resources/menu/
└── verticalMenu.json (updated)
```

## Database Schema

### Bảng `asset_types`

| Column | Type | Description |
|--------|------|-------------|
| id | bigint | Primary key |
| name | varchar(255) | Tên loại tài sản (unique) |
| description | text | Mô tả (nullable) |
| icon | varchar(255) | Icon class hoặc đường dẫn (nullable) |
| image | varchar(255) | Đường dẫn hình ảnh (nullable) |
| status | enum | active/inactive (default: active) |
| sort_order | integer | Thứ tự sắp xếp (default: 0) |
| created_at | timestamp | Ngày tạo |
| updated_at | timestamp | Ngày cập nhật |

### Indexes
- `asset_types_status_sort_order_index` (status, sort_order)
- `asset_types_name_index` (name)
- `asset_types_name_unique` (name - unique)

## API Endpoints

### Web Routes
```php
// Resource routes
GET    /admin/asset-types              // index
GET    /admin/asset-types/create       // create form
POST   /admin/asset-types              // store
GET    /admin/asset-types/{id}         // show
GET    /admin/asset-types/{id}/edit    // edit form
PUT    /admin/asset-types/{id}         // update
DELETE /admin/asset-types/{id}         // destroy

// Additional routes
GET    /admin/asset-types-data         // DataTables data
POST   /admin/asset-types/{id}/toggle-status  // Toggle status
POST   /admin/asset-types/update-sort-order   // Update sort order
GET    /admin/api/asset-types          // API for Select2
```

## Validation Rules

### Store Request
- `name`: required|string|max:255|unique:asset_types,name
- `description`: nullable|string|max:1000
- `icon`: nullable|string|max:255
- `image`: nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048
- `status`: required|in:active,inactive
- `sort_order`: nullable|integer|min:0

### Update Request
- `name`: required|string|max:255|unique:asset_types,name,{id}
- Các rules khác tương tự Store Request
- `remove_image`: nullable|boolean

## Model Features

### Relationships
```php
// Future relationship với Asset model
public function assets(): HasMany
```

### Scopes
```php
$assetTypes = AssetType::active()->ordered()->get();
$inactiveTypes = AssetType::inactive()->get();
```

### Accessors
```php
$assetType->status_badge        // HTML badge
$assetType->formatted_created_at // Formatted date
$assetType->icon_html           // Icon HTML
$assetType->image_url           // Full image URL
$assetType->display_name        // Name with icon
```

### Static Methods
```php
AssetType::getNextSortOrder()   // Get next sort order
AssetType::getActiveOptions()   // For dropdown options
```

## JavaScript Features

### DataTables Configuration
- Client-side processing
- Custom filtering (status)
- Export functionality
- Responsive design
- Action buttons integration

### Form Validation
- Real-time validation với FormValidation library
- Custom validation rules
- Icon format validation
- Image preview functionality

### Interactive Features
- Real-time preview card
- Toggle status với AJAX
- Delete confirmation với SweetAlert2
- Image upload preview
- Character counter

## CSS Customization

Module sử dụng file `resources/css/asset-types.css` cho custom styling:
- Preview card animations
- Form enhancements
- Responsive adjustments
- Dark mode support
- Loading states

## Cách Sử Dụng

### 1. Migration
```bash
php artisan migrate
```

### 2. Build Assets
```bash
npm run build
# hoặc cho development
npm run dev
```

### 3. Truy Cập Module
- Vào menu "Thành viên" > "Loại tài sản"
- Hoặc truy cập trực tiếp: `/admin/asset-types`

### 4. Tạo Loại Tài Sản Mới
1. Click "Thêm Mới"
2. Điền thông tin bắt buộc (Tên, Trạng thái)
3. Tùy chọn: thêm mô tả, icon, hình ảnh
4. Click "Tạo Mới"

### 5. Quản Lý Existing Records
- **Xem**: Click icon mắt
- **Sửa**: Click icon bút chì
- **Toggle Status**: Click icon play/pause
- **Xóa**: Click icon thùng rác

## Best Practices

### 1. Icon Usage
```php
// RemixIcon classes (recommended)
'icon' => 'ri-file-line'
'icon' => 'ri-folder-line'

// File paths (for custom icons)
'icon' => 'icons/custom-asset-type.svg'
```

### 2. Image Upload
- Kích thước tối đa: 2MB
- Định dạng: JPEG, PNG, JPG, GIF, SVG
- Tự động resize nếu cần

### 3. Sort Order
- Để trống để tự động tạo
- Số càng nhỏ hiển thị trước
- Có thể drag & drop (future feature)

## Troubleshooting

### 1. JavaScript Errors
- Đảm bảo đã build assets: `npm run build`
- Check console cho errors
- Verify Vite configuration

### 2. Validation Errors
- Check Form Request classes
- Verify database constraints
- Check file upload permissions

### 3. Image Upload Issues
- Check storage permissions
- Verify storage disk configuration
- Check file size limits

## Future Enhancements

1. **Drag & Drop Reordering**: Implement SortableJS
2. **Bulk Operations**: Select multiple records
3. **Import/Export**: CSV import functionality
4. **Asset Relationship**: Connect với Asset model
5. **Advanced Filtering**: More filter options
6. **API Integration**: RESTful API endpoints

## Standards Compliance

Module này tuân thủ đầy đủ các standards của dự án:
- ✅ CRUD_MODULE_TEMPLATE.md
- ✅ DATATABLES_IMPLEMENTATION_GUIDE.md
- ✅ FORM_COMPONENTS_GUIDE.md
- ✅ VPCC_COMPREHENSIVE_DEVELOPMENT_GUIDE.md

## Support

Để được hỗ trợ hoặc báo cáo bugs, vui lòng tham khảo:
- Development documentation trong `/docs`
- Code comments trong các files
- Standards và best practices đã thiết lập
